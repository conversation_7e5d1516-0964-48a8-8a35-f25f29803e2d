import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/calender/calendarAgendaScreen.dart';
import 'package:seawork/screens/employee/calender/calendarDayScreen.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/employee/providers/meetingProvider.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:timezone/data/latest.dart' as tz;

class CalendarScreen extends ConsumerStatefulWidget {
  const CalendarScreen({Key? key}) : super(key: key);

  @override
  _CalendarScreenState createState() => _CalendarScreenState();
}

class _CalendarScreenState extends ConsumerState<CalendarScreen> {
  final int _selectedIndex = 1;
  String selectedView = 'Agenda';
  double _dragOffset = 0;
  PageController? _scrollController;

  @override
  void initState() {
    super.initState();
    // Initialize timezone database
    tz.initializeTimeZones();

    // Reset to today's date and fetch events when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Always reset to today when calendar screen is opened
      ref.read(graphCalendarProvider.notifier).resetToToday();
    });
  }

  @override
  void dispose() {
    _scrollController?.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    final calendarState = ref.watch(graphCalendarProvider);
    final selectedDate = calendarState.selectedDate;
    final showMonthView = calendarState.showMonthView;

    ref.listen<CalendarState>(graphCalendarProvider, (previous, next) {
      if (previous != null && 
          previous.selectedDate != next.selectedDate &&
          _isToday(next.selectedDate) &&
          _scrollController != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToToday();
        });
      }
    });
    return PopScope(
  canPop: true,
  onPopInvoked: (didPop) {
    if (!didPop) {
      Navigator.pop(context);
    }
  },
  child: Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(title: 'Calendar', showBackButton: false),
      body: SafeArea(
        child: Column(
          children: [
            _buildMonthToggle(selectedDate),
            // Scrollable date row with indicator bar
            _buildScrollableDateRow(context, selectedDate),
            SizedBox(height: 8),

            // Show either day view or agenda view based on selection
            if (calendarState.isLoading)
              const Expanded(child: Center(child: CircularProgressIndicator()))
            else if (calendarState.error != null)
              Expanded(
                child: Center(child: Text('Error: ${calendarState.error}')),
              )
            else if (selectedView == 'Agenda')
              Expanded(child: AgendaScreen(selectedDate: selectedDate))
            else
              Expanded(child: DayScreen(selectedDate: selectedDate)),
          ],
          ),
        ),
        bottomNavigationBar: CustomBottomNavigationBar(onTap: (index) {}),
      )
    );
  }

  Widget _buildMonthToggle(DateTime selectedDate) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DmSansText(
            DateFormat('MMMM, yyyy').format(selectedDate),
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.viewColor,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildToggleButton('Agenda'),
              const SizedBox(width: 8),
              _buildToggleButton('Day'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildToggleButton(String label) {
    final bool isSelected = selectedView == label;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedView = label;
        });
      },
      child: Container(
        width: 84,
        height: 28,
        decoration: BoxDecoration(
          color: isSelected ? AppColors.viewColor : AppColors.whiteColor,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(color: AppColors.lightGreyColor2),
        ),
        alignment: Alignment.center,
        child: OpenSansText(
          label,
          color: isSelected ? AppColors.whiteColor : AppColors.viewColor,
          fontWeight: FontWeight.w400,
          fontSize: 12,
        ),
      ),
    );
  }

  // Scrollable Date Row Widget
  Widget _buildScrollableDateRow(BuildContext context, DateTime selectedDate) {
    return Consumer(
      builder: (context, ref, _) {
        final isExpanded = ref.watch(graphCalendarProvider).showMonthView;

        final today = DateTime.now();
        final startDate = _findPreviousSunday(
          today.subtract(const Duration(days: 60)),
        );
        final endDate = today.add(const Duration(days: 365 * 100));
        final totalDays = endDate.difference(startDate).inDays + 1;
        final pageCount = (totalDays / 7).ceil();

        final currentPage =
            (selectedDate.difference(startDate).inDays / 7).floor();
        if (_scrollController == null || _scrollController!.initialPage != currentPage) {
          _scrollController?.dispose();
          _scrollController = PageController(initialPage: currentPage);
        }
        final lastVisibleDate = startDate.add(
          Duration(days: currentPage * 7 + 6),
        );
        final monthViewStartDate = lastVisibleDate.add(const Duration(days: 1));

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Divider(
              height: 1,
              thickness: 0.5,
              color: AppColors.calenderdivColor,
            ),
            Container(
              color: AppColors.whiteColor,
              padding: const EdgeInsets.only(top: 12, bottom: 2),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children:
                        ['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day) {
                          return SizedBox(
                            width: 48,
                            child: Center(
                              child: OpenSansText(
                                day,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: AppColors.blackColor,
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 48,
                    child: PageView.builder(
                      controller: _scrollController,
                      itemCount: pageCount,
                      itemBuilder: (context, pageIndex) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: List.generate(7, (dayIndex) {
                            final date = startDate.add(
                              Duration(days: pageIndex * 7 + dayIndex),
                            );
                            return _buildDateItem(date, selectedDate, (
                              selected,
                            ) {
                              ref
                                  .read(graphCalendarProvider.notifier)
                                  .selectDate(selected);
                            });
                          }),
                        );
                      },
                    ),
                  ),
                  if (!isExpanded)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 2),
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          ref
                              .read(graphCalendarProvider.notifier)
                              .toggleCalendarView();
                        },
                        onVerticalDragUpdate: (details) {
                          if (details.delta.dy > 5) {
                            ref
                                .read(graphCalendarProvider.notifier)
                                .toggleCalendarView();
                          }
                        },
                        child: Container(
                          height: 12,
                          alignment: Alignment.center,
                          child: Container(
                            width: 38,
                            height: 4,
                            decoration: BoxDecoration(
                              color: AppColors.blackColor.withOpacity(0.5),
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const Divider(
              height: 1,
              thickness: 0.5,
              color: AppColors.calenderdivColor,
            ),

            if (isExpanded)
              _buildScrollableMonthView(
                context,
                selectedDate,
                monthViewStartDate,
              ),
          ],
        );
      },
    );
  }

  // Scrollable Month Calendar View with responsive height - fixed for iPhone SE
  Widget _buildScrollableMonthView(
    BuildContext context,
    DateTime selectedDate,
    DateTime startFromDate, // this is monthViewStartDate
  ) {
    final monthsToShow = 12;

    // Use startFromDate to set startMonth
    var startMonth = DateTime(startFromDate.year, startFromDate.month, 1);

    int selectedMonthIndex = 0;

    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    double heightPercentage;
    if (screenWidth <= 375 && screenHeight <= 667) {
      heightPercentage = 0.285;
    } else if (screenWidth <= 375) {
      heightPercentage = 0.32;
    } else {
      heightPercentage = 0.38;
    }

    return Consumer(
      builder: (context, ref, _) {
        final isExpanded = ref.watch(graphCalendarProvider).showMonthView;

        return Container(
          height: screenHeight * heightPercentage - 40,
          width: double.infinity,
          decoration: const BoxDecoration(
            color: AppColors.secondaryColor,
            border: Border(
              bottom: BorderSide(color: AppColors.calanderbordercolor),
            ),
          ),
          child: Column(
            children: [
              Expanded(
                child: PageView.builder(
                  controller: PageController(
                    initialPage: selectedMonthIndex,
                    viewportFraction: 1.0,
                  ),
                  physics: const BouncingScrollPhysics(),
                  itemCount: monthsToShow,
                  itemBuilder: (context, monthIndex) {
                    final currentMonth = DateTime(
                      startMonth.year,
                      startMonth.month + monthIndex,
                      1,
                    );
                    return _buildMonthGrid(
                      context,
                      currentMonth,
                      selectedDate,
                      isExpanded,
                    );
                  },
                ),
              ),
              if (!isExpanded)
                Padding(
                  padding: const EdgeInsets.only(bottom: 1),
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      ref
                          .read(graphCalendarProvider.notifier)
                          .toggleCalendarView();
                    },
                    onVerticalDragUpdate: (details) {
                      _dragOffset += details.delta.dy;
                      if (_dragOffset > 30) {
                        ref
                            .read(graphCalendarProvider.notifier)
                            .toggleCalendarView();
                        _dragOffset = 0;
                      }
                    },
                    onVerticalDragEnd: (_) {
                      _dragOffset = 0;
                    },
                    child: Container(
                      width: 38,
                      height: 4,
                      decoration: BoxDecoration(
                        color: AppColors.draggablebarcolor,
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  // Helper to build a single month grid - optimized for iPhone SE
  Widget _buildMonthGrid(
    BuildContext context,
    DateTime month,
    DateTime selectedDate,
    bool isExpanded,
  ) {
    final today = DateTime.now();
    final firstDayOfMonth = DateTime(month.year, month.month, 1);

    // Get the day of week for the first day (0 = Sunday, 6 = Saturday)
    final firstWeekdayOfMonth = firstDayOfMonth.weekday % 7;

    // Calculate the first day to show (might be from previous month)
    final firstDayToShow = firstDayOfMonth.subtract(
      Duration(days: firstWeekdayOfMonth),
    );

    // Generate dates for 5 weeks only (35 days)
    const totalDaysToShow = 35;
    final allDatesToShow = List.generate(
      totalDaysToShow,
      (index) => firstDayToShow.add(Duration(days: index)),
    );

    // Group dates by week (5 weeks)
    final weeks = <List<DateTime>>[];
    for (var i = 0; i < allDatesToShow.length; i += 7) {
      weeks.add(allDatesToShow.sublist(i, min(i + 7, allDatesToShow.length)));
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children:
                  weeks.map((weekDates) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children:
                          weekDates.map((date) {
                            final isCurrentMonth = date.month == month.month;
                            final isFirstDayOfMonth = date.day == 1;
                            final monthName = DateFormat('MMM').format(date);
                            final isSelected =
                                date.year == selectedDate.year &&
                                date.month == selectedDate.month &&
                                date.day == selectedDate.day;
                            final isToday =
                                date.year == today.year &&
                                date.month == today.month &&
                                date.day == today.day;

                            return Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  ref
                                      .read(graphCalendarProvider.notifier)
                                      .selectDate(date);
                                  ref
                                      .read(graphCalendarProvider.notifier)
                                      .toggleCalendarView();
                                },
                                child: Container(
                                  margin: const EdgeInsets.all(2),
                                  padding: const EdgeInsets.all(5),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color:
                                        isSelected
                                            ? AppColors.selecteddatecolor
                                            : Colors.transparent,
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (isFirstDayOfMonth)
                                        OpenSansText(
                                          monthName,
                                          fontSize: 10,
                                          color: AppColors.blackColor,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      OpenSansText(
                                        date.day.toString(),
                                        fontSize: 14,
                                        color:
                                            !isCurrentMonth
                                                ? AppColors.draggablebarcolor
                                                : isSelected
                                                ? Colors.white
                                                : Colors.black,
                                        fontWeight:
                                            isSelected || isToday
                                                ? FontWeight.bold
                                                : FontWeight.normal,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                    );
                  }).toList(),
            ),
          ),

          // Drag handle inside grid when expanded
          if (isExpanded)
            GestureDetector(
              behavior: HitTestBehavior.translucent, // expands hit area
              onTap: () {
                ref.read(graphCalendarProvider.notifier).toggleCalendarView();
              },
              onVerticalDragUpdate: (details) {
                _dragOffset += details.delta.dy;

                if (_dragOffset < -30) {
                  // more sensitive upward drag
                  ref.read(graphCalendarProvider.notifier).toggleCalendarView();
                  _dragOffset = 0;
                }
              },
              onVerticalDragEnd: (_) {
                _dragOffset = 0;
              },
              child: Container(
                padding: const EdgeInsets.only(top: 16, bottom: 2),
                alignment: Alignment.center,
                child: Container(
                  width: 38,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.blackColor.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDateItem(
    DateTime date,
    DateTime selectedDate,
    Function(DateTime) onDateSelected,
  ) {
    final isSelected =
        date.year == selectedDate.year &&
        date.month == selectedDate.month &&
        date.day == selectedDate.day;
    final isToday = _isToday(date);

    return GestureDetector(
      onTap: () => onDateSelected(date),
      child: SizedBox(
        width: 48,
        child: Column(
          children: [
            const SizedBox(height: 4),
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isSelected ? AppColors.viewColor : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
                border:
                    isToday
                        ? Border.all(
                          color:
                              isSelected
                                  ? Colors.transparent
                                  : AppColors.blackColor,
                          width: 1,
                        )
                        : null,
              ),
              child: Center(
                child: OpenSansText(
                  date.day.toString(),
                  color:
                      isSelected
                          ? AppColors.secondaryColor
                          : AppColors.lightBlack,
                  fontWeight: FontWeight.w400,
                  fontSize: 20,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper function to scroll to today's date
  void _scrollToToday() {
    if (_scrollController == null) return;
    
    final today = DateTime.now();
    final startDate = _findPreviousSunday(
      today.subtract(const Duration(days: 60)),
    );
    final currentPage = (today.difference(startDate).inDays / 7).floor();
    
    if (_scrollController!.hasClients) {
      _scrollController!.animateToPage(
        currentPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  // Helper function to find previous Sunday
  DateTime _findPreviousSunday(DateTime date) {
    return date.subtract(Duration(days: date.weekday % 7));
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  // Helper function to get minimum of two values
  int min(int a, int b) {
    return a < b ? a : b;
  }
}