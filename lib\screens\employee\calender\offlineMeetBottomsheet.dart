import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/widget/customTextFieldWithHeading.dart';
import 'package:seawork/components/widget/customWhite&BlueBorderElevatedButton.dart';
import 'package:seawork/components/widget/customBlueButton.dart';
import 'package:seawork/screens/employee/employee/providers/meetingProvider.dart';

class RSVPBottomSheet extends ConsumerStatefulWidget {
  final String eventId;

  const RSVPBottomSheet({super.key, required this.eventId});

  @override
  ConsumerState<RSVPBottomSheet> createState() => _RSVPBottomSheetState();
}

class _RSVPBottomSheetState extends ConsumerState<RSVPBottomSheet> {
  final TextEditingController _remarksController = TextEditingController();
  bool _notifyOrganizer = true;

  @override
  void dispose() {
    _remarksController.dispose();
    super.dispose();
  }

  Future<void> _handleAccept() async {
    final comment =
        _remarksController.text.trim().isEmpty
            ? null
            : _remarksController.text.trim();

    final success = await ref
        .read(graphCalendarProvider.notifier)
        .acceptMeeting(
          widget.eventId,
          comment: comment,
          sendResponse: _notifyOrganizer,
        );

    if (success) {
      Navigator.pop(context, "Accepted");
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Meeting accepted successfully'),
          backgroundColor: AppColors.green,
        ),
      );
    } else {
      final errorMessage =
          ref.read(graphCalendarProvider).error ?? 'Failed to accept meeting';
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(errorMessage), backgroundColor: Colors.red),
      );
    }
  }

  Future<void> _handleDecline() async {
    final comment =
        _remarksController.text.trim().isEmpty
            ? null
            : _remarksController.text.trim();

    final success = await ref
        .read(graphCalendarProvider.notifier)
        .declineMeeting(
          widget.eventId,
          comment: comment,
          sendResponse: _notifyOrganizer,
        );

    if (success) {
      Navigator.pop(context, "Declined");
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Meeting declined successfully'),
          backgroundColor: AppColors.green,
        ),
      );
    } else {
      final errorMessage =
          ref.read(graphCalendarProvider).error ?? 'Failed to decline meeting';
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(errorMessage), backgroundColor: Colors.red),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final calendarState = ref.watch(graphCalendarProvider);
    final isLoading = calendarState.isRsvpLoading;

    return Container(
      decoration: const BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Center(
            child: CustomPngImages(
              imageName: "bottom_line",
              width: 30,
              height: 5,
            ),
          ),
          const SizedBox(height: 24),

          DmSansText(
            "RSVP",
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.viewColor,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: MediaQuery.of(context).size.height * 0.02),

          // Notify Organizer Toggle
          Container(
            decoration: BoxDecoration(
              color: AppColors.whiteColor,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(color: AppColors.lightGreyColor2),
            ),
            child: SizedBox(
              width: double.infinity,
              height: 43,
              child: Card(
                color: AppColors.whiteColor,
                elevation: 0,
                margin: EdgeInsets.zero,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 16.0),
                      child: OpenSansText(
                        "Notify organizer",
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: AppColors.blackColor,
                      ),
                    ),
                    Transform.scale(
                      scale: 0.8,
                      child: Switch(
                        value: _notifyOrganizer,
                        onChanged:
                            isLoading
                                ? null
                                : (bool value) {
                                  setState(() {
                                    _notifyOrganizer = value;
                                  });
                                },
                        activeTrackColor: AppColors.calanderbordercolor,
                        inactiveTrackColor: AppColors.calanderbordercolor,
                        activeColor: Colors.black,
                        inactiveThumbColor: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Remarks Field
          Material(
            color: Colors.transparent, // Ensures it blends with the design
            child: CustomTextFieldWithHeading(
              Heading: 'Remarks (Optional)',
              hintText: 'Enter',
              hasAsterisk: false,
              controller: _remarksController,
              maxlines: 5,
              fillColor: Colors.white,
              hintColor: AppColors.lightGreyshade,
              hintStyle: AppColors.lightGreyshade,
              headingFontWeight: FontWeight.w400,
              enabled: !isLoading,
            ),
          ),

          SizedBox(height: 34),

          // Accept Button
          SizedBox(
            width: double.infinity,
            child:
                isLoading
                    ? Container(
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppColors.lightGreyColor2,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Center(
                        child: SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppColors.viewColor,
                            ),
                          ),
                        ),
                      ),
                    )
                    : SubmitRequestButton(
                      text: "Accept",
                      onPressed: _handleAccept,
                    ),
          ),
          SizedBox(height: 8),
          // Decline Button
          SizedBox(
            width: double.infinity,
            child:
                isLoading
                    ? Container(
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppColors.lightGreyColor2,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: AppColors.lightGreyColor2),
                      ),
                      child: const Center(
                        child: SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppColors.viewColor,
                            ),
                          ),
                        ),
                      ),
                    )
                    : ViewDeclineButton(
                      text: "Decline",
                      onPressed: _handleDecline,
                    ),
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.01,
          ), // 1% of screen height
          // Maybe Option (Text Button)
          // TextButton(
          //   onPressed: () {
          //     Navigator.pop(context, "Maybe");
          //   },
          //   child: OpenSansText(
          //     "Maybe",
          //     fontSize: 14,
          //     color: AppColors.viewColor,
          //     fontWeight: FontWeight.w400,
          //   ),
          // ),
        ],
      ),
    );
  }
}