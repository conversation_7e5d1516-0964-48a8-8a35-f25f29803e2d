import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/screens/service/ticket/models/serviceCategory.dart';
import 'package:seawork/screens/service/ticket/ticketCategorySelection.dart';
import 'package:seawork/utils/style/sizeConfig.dart';
import 'package:seawork/screens/service/ticket/ticketDetailsSheetSearch.dart';
import 'package:universal_html/html.dart' as html;
import 'package:seawork/screens/service/ticket/models/ticketDetails.dart';
import 'package:seawork/screens/service/ticket/models/ticketSearchResult.dart';
import 'package:seawork/screens/service/ticket/providers/ticketProviders.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/commonWidgets/customIcon.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/foundation.dart';

class SearchTickets extends ConsumerStatefulWidget {
  const SearchTickets({super.key});

  @override
  ConsumerState<SearchTickets> createState() => _SearchTicketsState();
}

mixin _SearchDebounce {
  Timer? _debounceTimer;

  void runDebounced(VoidCallback callback, {int delay = 500}) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(Duration(milliseconds: delay), callback);
  }

  void disposeDebouncer() {
    _debounceTimer?.cancel();
    _debounceTimer = null;
  }
}

class _SearchTicketsState extends ConsumerState<SearchTickets>
    with _SearchDebounce {
  final TextEditingController _searchController = TextEditingController();
  int _selectedFilter = 0;
  bool _isSearching = false;

  final List<Map<String, dynamic>> _services = [
    {
      'title': 'HCM- Configuration change',
      'icon': 'assets/images/services.png',
    },
    {'title': 'Network Access Request', 'icon': 'assets/images/services.png'},
    {
      'title': 'HCM- Configuration change',
      'icon': 'assets/images/services.png',
    },
    {'title': 'Network Access Request', 'icon': 'assets/images/services.png'},
  ];
  final List<Map<String, dynamic>> _articles = [
    {'title': 'How to reset password', 'icon': 'assets/images/article.png'},
    {'title': 'VPN setup guide', 'icon': 'assets/images/article.png'},
  ];

  final List<Map<String, dynamic>> _tickets = [
    {'title': 'How to reset password', 'icon': 'assets/images/ticket.png'},
    {'title': 'VPN setup guide', 'icon': 'assets/images/ticket.png'},
  ];

  bool _isViewingAll = false;
  String? _currentViewAllSection;

  @override
  void didChangeDependencies() {
    SizeConfig.init(context);
    super.didChangeDependencies();
  }

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();

    if (query.isEmpty) {
      // Clear search results if the query is empty
      setState(() {
        _isSearching = false;
      });
      return;
    }

    // Show loading indicator immediately
    setState(() {
      _isSearching = true;
    });

    // Use the debouncer to delay the actual search
    runDebounced(() {
      print("Searching for: $query");
      ref.read(searchProvider(query).notifier).search(query);

      // We'll keep _isSearching true until the search completes
      // The loading state will be handled by the FutureBuilder/AsyncValue
    });
  }

  void _updateFilter(int index) {
    setState(() {
      _selectedFilter = index;
    });
  }

  @override
  void dispose() {
    disposeDebouncer();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteColor,
      appBar: AppBar(
        backgroundColor: AppColors.whiteColor,
        leading: IconButton(
          icon: const CustomSvgImage(imageName: 'appbackbutton'),
          onPressed: () {
            if (_isViewingAll) {
              setState(() {
                _isViewingAll = false;
                _currentViewAllSection = null;
              });
            } else {
              Navigator.pop(context);
            }
          },
        ),
        titleSpacing: 0,
        title: Padding(
          padding: const EdgeInsets.only(top: 2, right: 12),
          child: Container(
            height: 37,
            // margin: const EdgeInsets.only(right: 2, left: 2),
            decoration: BoxDecoration(
              color: AppColors.inputfillColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextField(
              autofocus: false,
              controller: _searchController,
              decoration: InputDecoration(
                filled: true,
                fillColor: AppColors.inputfillColor,
                hintText: 'Search services, article and tickets',
                hintStyle: GoogleFonts.openSans(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: AppColors.searchfieldtext,
                ),
                contentPadding: EdgeInsets.only(top: 8, bottom: 8, left: 8),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Close/Clear button
                          GestureDetector(
                            onTap: () {
                              _searchController.clear();
                              setState(() {
                                _isSearching = false;
                              });
                              disposeDebouncer();
                            },
                            child: Padding(
                              padding: EdgeInsets.only(right: 8),
                              child: Icon(
                                Icons.close,
                                size: 20,
                                color: AppColors.searchfieldtext,
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(right: 12),
                            child: CustomSvgImage(imageName: "search_icon"),
                          ),
                        ],
                      )
                    : Padding(
                        padding: EdgeInsets.only(right: 12),
                        child: CustomSvgImage(imageName: "search_icon"),
                      ),
                suffixIconConstraints: const BoxConstraints(
                  minWidth: 20,
                  minHeight: 20,
                ),
              ),
              textInputAction: TextInputAction.search,
              onSubmitted: (_) {
                // Force search on keyboard submit
                if (_searchController.text.isNotEmpty) {
                  setState(() => _isSearching = true);
                  ref
                      .read(searchProvider(_searchController.text).notifier)
                      .search(_searchController.text);
                }
              },
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          SizedBox(height: 5.h),
          Visibility(
            visible: _searchController.text.isNotEmpty,
            child: Container(
              width: 393.w,
              height: 53.h,
              color: AppColors.inputfillColor,
              child: Row(
                children: [
                  _buildFilterOption('All', 0),
                  _buildFilterOption('Articles', 1),
                  _buildFilterOption('Services', 2),
                  _buildFilterOption('Tickets', 3),
                ],
              ),
            ),
          ),
          Expanded(
            child: ref
                .watch(searchProvider(_searchController.text))
                .when(
                  data: (results) {
                    // Hide the loading indicator once we get results
                    if (_isSearching) {
                      setState(() => _isSearching = false);
                    }

                    print("Got ${results.length} search results");

                    if (_isViewingAll && _currentViewAllSection != null) {
                      final sectionItems =
                          _currentViewAllSection == 'Services'
                              ? results
                                  .where((r) => r.useType == 'service')
                                  .toList()
                              : _currentViewAllSection == 'Articles'
                              ? results
                                  .where((r) => r.useType == 'article')
                                  .toList()
                              : results
                                  .where((r) => r.useType == 'ticket')
                                  .toList();

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Row(
                              children: [
                                // IconButton(
                                //   icon: const Icon(Icons.arrow_back),
                                //   onPressed: () {
                                //     setState(() {
                                //       _isViewingAll = false;
                                //       _currentViewAllSection = null;
                                //     });
                                //   },
                                // ),
                                OpenSansText(
                                  "All $_currentViewAllSection",
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                  color: AppColors.viewColor,
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            child: ListView.builder(
                              keyboardDismissBehavior:
                                  ScrollViewKeyboardDismissBehavior.onDrag,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                              ),
                              itemCount: sectionItems.length,
                              itemBuilder: (context, index) {
                                final item = sectionItems[index];
                                if (_currentViewAllSection == 'Services') {
                                  return _buildServiceItem(item);
                                } else if (_currentViewAllSection ==
                                    'Articles') {
                                  return _buildArticleItem(item);
                                } else {
                                  return _buildTicketItem(item);
                                }
                              },
                            ),
                          ),
                        ],
                      );
                    }

                    return _buildSearchResults(results);
                  },
                  loading:
                      () => Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const CircularProgressIndicator(),
                            const SizedBox(height: 16),
                            OpenSansText(
                              'Searching...',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: AppColors.viewColor,
                            ),
                          ],
                        ),
                      ),
                  error: (error, _) => Center(child: Text('Error: $error')),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterOption(String text, int index) {
    return GestureDetector(
      onTap: () => _updateFilter(index),
      child: Container(
        margin: const EdgeInsets.fromLTRB(8, 8, 0, 8),
        padding: const EdgeInsets.symmetric(horizontal: 10),
        decoration: BoxDecoration(
          color:
              _selectedFilter == index
                  ? AppColors.viewColor
                  : AppColors.whiteColor,
          borderRadius: BorderRadius.circular(25),
        ),
        child: Row(
          children: [
            if (index != 0) ...[
              SvgPicture.asset(
                _selectedFilter == index
                    ? 'assets/images/${text.toLowerCase()}-selected.svg'
                    : 'assets/images/${text.toLowerCase()}.svg',
              ),
              const SizedBox(width: 4),
            ],
            Container(
              height: 28, // Setting consistent height for all options
              alignment: Alignment.center,
              child: DmSansText(
                text,
                fontSize: 12,
                color:
                    _selectedFilter == index
                        ? AppColors.whiteColor
                        : AppColors.viewColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults(List<SearchResult> results) {
    if (_searchController.text.isEmpty) {
      return Container();
    }
    final services = results.where((r) => r.useType == 'service').toList();
    final articles = results.where((r) => r.useType == 'article').toList();
    final tickets = results.where((r) => r.useType == 'ticket').toList();

    if (services.isEmpty && articles.isEmpty && tickets.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.only(bottom: 74),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomSvgImage(imageName: 'ic_nosearchresult'),
              const SizedBox(height: 8),
              OpenSansText(
                'No search results found',
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppColors.lightGreyColor,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return ListView(
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      children: [
        if (_shouldShowSection(2) && services.isNotEmpty)
          _buildSection('Services', services, _buildServiceItem),
        if (_shouldShowSection(1) && articles.isNotEmpty)
          _buildSection('Articles', articles, _buildArticleItem),
        if (_shouldShowSection(3) && tickets.isNotEmpty)
          _buildSection('Tickets', tickets, _buildTicketItem),
      ],
    );
  }

  bool _shouldShowSection(int filterIndex) {
    return _selectedFilter == 0 || _selectedFilter == filterIndex;
  }

  Widget _buildSection(
    String title,
    List<SearchResult> items,
    Widget Function(SearchResult) builder,
  ) {
    final isTicketView = _selectedFilter == 3 || _isViewingAll;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(
          title,
          items.length > 2 ? () => _showAllItems(title, items) : null,
        ),
        if (title == 'Tickets' && isTicketView)
          _buildTicketSection(items)
        else
          ...items.take(2).map(builder).toList(),
      ],
    );
  }

  Widget _buildSectionHeader(String title, VoidCallback? onViewAll) {
    return Padding(
      padding: const EdgeInsets.only(top: 8, bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
        children: [
          Text(
            title,
            style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: AppColors.viewColor,
            decoration: TextDecoration.underline,
            decorationColor: Colors.white,
            ),
          ),
          if (onViewAll != null)
            TextButton(
              onPressed: onViewAll,
              child: Text(
                'View all',
                style: GoogleFonts.openSans(
                  fontSize: 12,
                  decoration: TextDecoration.underline,
                  color: AppColors.viewColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildServiceItem(SearchResult item) {
    return GestureDetector(
      // onTap: () {
      //   context.push(
      //     '/request-and-report',
      //     extra: {
      //       'itilTicketTypeId': 3,
      //       'serviceCategoryId': 13,
      //       'iconClicked': "Request a service",
      //       'requestType': "IT service request",
      //       'prefilledDescription': item.title,
      //     },
      //   );
      // },
      onTap: () {
        final serviceCategory = ServiceCategory(
          guid: '',
          id: item.serviceCategoryId!,
          name: item.name ?? '',
          summary: item.summary,
          important: false,
        );
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TicketRequestPage(category: serviceCategory),
          ),
        );
      },
      child: Container(
        width: 353.w,
        height: 71.h,
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Color(0x4080CAD6),
              blurRadius: 9.6,
              offset: Offset(0, 0),
            ),
          ],
        ),
        child: Row(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: CustomIcon(
                imagePath:
                    // _selectedFilter == 2
                    //     ? 'assets/images/services-selected.svg'
                    'assets/images/services.svg',
              ),
            ),
            Expanded(
              child: DmSansText(
                item.title,
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.headingColor,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildArticleItem(SearchResult item) {
    return GestureDetector(
      onTap: () async {
        final url =
            'https://seacrmdev.haloitsm.com/portal/kb?btn=61&id=${item.id}';

        try {
          if (kIsWeb) {
            html.window.open(url, '_blank');
          } else {
            // Use url_launcher package for mobile
            if (!await launchUrl(Uri.parse(url))) {
              throw Exception('Could not launch $url');
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error opening URL: $e');
          }
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Could not open article: ${e.toString()}')),
          );
        }
      },
      child: Container(
        width: 353.w,
        height: 71.h,
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Color(0x4080CAD6),
              blurRadius: 9.6,
              offset: Offset(0, 0),
            ),
          ],
        ),
        child: Row(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: CustomIcon(
                imagePath:
                    //     _selectedFilter == 1
                    //         ? 'assets/images/articles-selected.svg'
                    'assets/images/articles.svg',
              ),
            ),
            Expanded(
              child: DmSansText(
                item.title,
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.headingColor,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTicketItem(SearchResult item) {
    return GestureDetector(
      onTap: () {
        ref.read(ticketDetailsProvider(item.id).future).then((ticketDetails) {
          _showTicketDetails(ticketDetails, ref);
        });
      },
      child: Container(
        width: 353.w,
        height: 71.h,
        margin: EdgeInsets.only(bottom: 8.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.w),
          boxShadow: const [
            BoxShadow(
              color: Color(0x4080CAD6),
              blurRadius: 9.6,
              offset: Offset(0, 0),
            ),
          ],
        ),
        child: Row(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: CustomIcon(
                imagePath:
                    // _selectedFilter == 3
                    //     ? 'assets/images/tickets-selected.svg'
                    'assets/images/tickets.svg',
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: 12.w),
                child: DmSansText(
                  item.title,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.headingColor,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAllItems(String type, List<SearchResult> items) {
    setState(() {
      _isViewingAll = true;
      _currentViewAllSection = type;
    });
  }

  Widget _buildTicketSection(List<SearchResult> tickets) {
    final ticketIds = tickets.map((t) => t.id).toList();
    return Consumer(
      builder: (context, ref, child) {
        final ticketsAsync = ref.watch(
          multipleTicketDetailsProvider(ticketIds),
        );
        return ticketsAsync.when(
          data:
              (ticketDetails) => Column(
                children:
                    ticketDetails
                        .map((ticket) => _buildTicketCard(ticket, ref))
                        .toList(),
              ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, _) => Text('Error loading tickets: $error'),
        );
      },
    );
  }

  Widget _buildTicketCard(TicketDetails ticket, WidgetRef ref) {
    return GestureDetector(
      onTap: () => _showTicketDetails(ticket, ref),
      child: Container(
        width: 353.w,
        height: 71.h,
        margin: EdgeInsets.only(bottom: 8.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.w),
          boxShadow: const [
            BoxShadow(
              color: Color(0x4080CAD6),
              blurRadius: 9.6,
              offset: Offset(0, 0),
            ),
          ],
        ),
        child: Row(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: CustomIcon(imagePath: 'assets/images/tickets.svg'),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: 12.w),
                child: DmSansText(
                  '#${ticket.id} - ${ticket.summary}',
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.headingColor,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(int? statusId) {
    final status = _getStatusFromId(statusId);
    final backgroundColor = _getStatusColor(statusId);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        status,
        style: const TextStyle(fontSize: 12, color: AppColors.headingColor),
      ),
    );
  }

  String _getStatusFromId(int? statusId) {
    if (statusId == 1) return 'Open';
    if (statusId == 9) return 'Closed';
    return 'In Progress';
  }

  Color _getStatusColor(int? statusId) {
    switch (statusId) {
      case 1: // New
        return const Color(0xFFCDECF4);
      case 9: // Closed
        return const Color(0xFFC6FDF0);
      default: // In Progress
        return const Color(0xFFFFF0BD);
    }
  }

  String _mapWorkflowStep(int step) {
    switch (step) {
      case 1:
        return 'Created';
      case 2:
        return 'In progress';
      case 3:
        return 'Resolved';
      default:
        return 'Created';
    }
  }

  void _showTicketDetails(TicketDetails ticket, WidgetRef ref) {
    showModalBottomSheet(
      context: ref.context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            builder:
                (_, controller) => TicketDetailsSheetForSearch(ticket: ticket),
          ),
    );
  }
}
