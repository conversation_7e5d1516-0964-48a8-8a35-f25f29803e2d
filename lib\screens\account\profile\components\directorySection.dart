import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/components/widget/customLoader.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/account/profile/provider/profileProvider.dart';

class DirectorySection extends ConsumerStatefulWidget {
  final List<Map<String, dynamic>>? people;
  final String? searchQuery;
  final ScrollController? controller;
  final bool isLoading;
  final bool hasMore;
  const DirectorySection({Key? key, this.people, this.searchQuery,this.controller,
    this.isLoading = false,
    this.hasMore = false,})
    : super(key: key);

  @override
  ConsumerState<DirectorySection> createState() => _DirectorySectionState();
}

class _DirectorySectionState extends ConsumerState<DirectorySection> {
  String? _pressedPersonId;

  @override
  Widget build(BuildContext context) {
    // final employeesAsync = ref.watch(allEmployeesProvider);

        final employees = widget.people ?? [];


        // Filter employees based on search query if provided
    List<Map<String, dynamic>> filteredEmployees = employees;
    if (widget.searchQuery != null && widget.searchQuery!.isNotEmpty) {
      final searchLower = widget.searchQuery!.toLowerCase();
      filteredEmployees = employees.where((employee) {
        final displayName = employee['DisplayName']?.toString().toLowerCase() ?? '';
        final workEmail = employee['WorkEmail']?.toString().toLowerCase() ?? '';
        final city = employee['City']?.toString().toLowerCase() ?? '';
        final addressLine1 = employee['AddressLine1']?.toString().toLowerCase() ?? '';
        final addressLine2 = employee['AddressLine2']?.toString().toLowerCase() ?? '';
        final addressLine3 = employee['AddressLine3']?.toString().toLowerCase() ?? '';

        return displayName.contains(searchLower) ||
            workEmail.contains(searchLower) ||
            city.contains(searchLower) ||
            addressLine1.contains(searchLower) ||
            addressLine2.contains(searchLower) ||
            addressLine3.contains(searchLower);
      }).toList();
    }

     // Sort employees by DisplayName
    final sortedPeople = List<Map<String, dynamic>>.from(filteredEmployees);
    sortedPeople.sort((a, b) {
      final nameA = a['DisplayName']?.toString() ?? '';
      final nameB = b['DisplayName']?.toString() ?? '';
      return nameA.compareTo(nameB);
    });

    if (widget.isLoading && sortedPeople.isEmpty) {
      // Show loading spinner when initially loading with no data
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
CustomLoadingWidget()     ,         SizedBox(height: 16),
              OpenSans500Large(
                12,
                'Loading employees...',
                AppColors.blackColor.withOpacity(0.6),
              ),
            ],
          ),
        ),
      );
    }

    
    if (sortedPeople.isEmpty) {
      // No employees found message
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.people_outline,
                size: 48,
                color: AppColors.blackColor.withOpacity(0.6),
              ),
              SizedBox(height: 16),
              OpenSans600Large(
                14,
                'No employees found',
                AppColors.blackColor.withOpacity(0.6),
              ),
            ],
          ),
        ),
      );
    }

    // Group by first letter
    final Map<String, List<Map<String, dynamic>>> groupedPeople = {};
    for (var person in sortedPeople) {
      final displayName = person['DisplayName']?.toString() ?? '';
      String initial = displayName.isNotEmpty ? displayName[0].toUpperCase() : '#';
      groupedPeople.putIfAbsent(initial, () => []).add(person);
    }


        final List<_GroupItem> itemList = [];
groupedPeople.entries.forEach((entry) {
      // Add header first
      itemList.add(_GroupItem.isHeader(entry.key));
      // Then add items
      for (var person in entry.value) {
        itemList.add(_GroupItem.isPerson(person));
      }
    });

    // Add an extra item to show loading spinner if hasMore
    final totalItemCount = itemList.length + (widget.hasMore ? 1 : 0);

    return Padding(
  padding: const EdgeInsets.symmetric(horizontal: 20),
  child: Column(
    children: [
      Expanded(
        child: ListView.builder(
          controller: widget.controller,
          itemCount: totalItemCount,
          itemBuilder: (context, index) {
            if (index == itemList.length) {
              return Padding(
                padding: const EdgeInsets.all(16),
                child: Center(child: CustomLoadingWidget()),
              );
            }
            final groupItem = itemList[index];
            if (groupItem.isHeader) {
              return Padding(
                padding: const EdgeInsets.only(top: 16, bottom: 8),
                child: OpenSans600Large(12, groupItem.header!, AppColors.blackColor),
              );
            } else {
              return _buildPersonCard(context, groupItem.person!);
            }
          },
        ),
      ),
    ],
  ),
);


    // return Padding(
    //   padding: const EdgeInsets.symmetric(horizontal: 20),
    //   child: ListView.builder(
    //     controller: widget.controller,
    //     itemCount: totalItemCount,
    //     itemBuilder: (context, index) {
    //       if (index == itemList.length) {
    //         // Loading footer
    //         return Padding(
    //           padding: const EdgeInsets.all(16),
    //           child: Center(child: CircularProgressIndicator()),
    //         );
    //       }

    //       final groupItem = itemList[index];
    //       if (groupItem.isHeader) {
    //         return Padding(
    //           padding: const EdgeInsets.only(top: 16, bottom: 8),
    //           child: OpenSans600Large(12, groupItem.header!, AppColors.blackColor),
    //         );
    //       } else {
    //         return _buildPersonCard(context, groupItem.person!);
    //       }
    //     },
    //   ),
    // );
  }

  
  Widget _buildPersonCard(BuildContext context, Map<String, dynamic> person) {
    final personId = person['PersonId']?.toString() ?? '';
    bool isPressed = _pressedPersonId == personId;

    // Extract data from API response
    final displayName = person['DisplayName']?.toString() ?? 'Unknown';
    final city = person['City']?.toString() ?? '';
    final addressLine1 = person['AddressLine1']?.toString() ?? '';
    final addressLine2 = person['AddressLine2']?.toString() ?? '';
    final addressLine3 = person['AddressLine3']?.toString() ?? '';

    // Combine address parts (3,2,1) with commas
    List<String> addressParts = [];
    if (addressLine3.isNotEmpty) addressParts.add(addressLine3);
    if (addressLine2.isNotEmpty) addressParts.add(addressLine2);
    if (addressLine1.isNotEmpty) addressParts.add(addressLine1);
    if (city.isNotEmpty) addressParts.add(city);

    String location = '';
    if (addressParts.isNotEmpty) {
      location = addressParts.join(', ');
      if (location.length > 40) {
        location = location.substring(0, 37) + '...';
      }
    } else if (city.isNotEmpty) {
      location = city;
    } else {
      location = 'No location';
    }

    return GestureDetector(
      onTapDown: (_) {
        setState(() {
          _pressedPersonId = personId;
        });
      },
      onTapUp: (_) {
        setState(() {
          _pressedPersonId = null;
        });
        context.push('/profile-info/$personId');
      },
      onTapCancel: () {
        setState(() {
          _pressedPersonId = null;
        });
      },
      child: AnimatedContainer(
        duration: Duration(milliseconds: 100),
        height: 72,
        margin: EdgeInsets.zero,
        decoration: BoxDecoration(
          color: isPressed ? AppColors.microinteraction : AppColors.whiteColor,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(color: AppColors.peopleboxshadow, blurRadius: 9.6),
          ],
        ),
        child: Row(
          children: [
            Padding(
              padding: EdgeInsets.all(10),
              child:  CustomPngImage(
                imageName: "peopleplaceholder",
                width: 40,
                height: 40,
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OpenSans600Large(14, displayName, AppColors.blackColor),
                SizedBox(height: 8),
                OpenSans400Large(12, location, AppColors.darkGreyColor),
              ],
            ),
          ],
        ),
      )
    
      // child: AnimatedContainer(
      //   duration: const Duration(milliseconds: 100),
      //   height: 72,
      //   margin: EdgeInsets.zero,
      //   decoration: BoxDecoration(
      //     color: isPressed ? AppColors.microinteraction : AppColors.whiteColor,
      //     borderRadius: BorderRadius.circular(8),
      //     boxShadow: [
      //       BoxShadow(color: AppColors.peopleboxshadow, blurRadius: 9.6),
      //     ],
      //   ),
      //   child: Row(
      //     children: [
      //       const Padding(
      //         padding: EdgeInsets.all(10),
      //         child: CircleAvatar(
      //           radius: 26.5,
      //           child: Icon(Icons.person, size: 40),
      //           backgroundColor: Colors.grey,
      //         ),
      //       ),
      //       Column(
      //         crossAxisAlignment: CrossAxisAlignment.start,
      //         mainAxisAlignment: MainAxisAlignment.center,
      //         children: [
      //           OpenSans600Large(14, displayName, AppColors.blackColor),
      //           const SizedBox(height: 8),
      //           OpenSans400Large(12, location, AppColors.darkGreyColor),
      //         ],
      //       ),
      //     ],
      //   ),
      // ),
    );
  }

}
    // return employeesAsync.when(
    //   data: (employees) => _buildDirectoryContent(employees),
    //   loading:
    //       () => Padding(
    //         padding: EdgeInsets.symmetric(horizontal: 20),
    //         child: Center(
    //           child: Column(
    //             mainAxisAlignment: MainAxisAlignment.center,
    //             children: [
    //               CircularProgressIndicator(color: AppColors.blackColor),
    //               SizedBox(height: 16),
    //               OpenSans500Large(
    //                 12,
    //                 'Loading employees...',
    //                 AppColors.blackColor.withOpacity(0.6),
    //               ),
    //             ],
    //           ),
    //         ),
    //       ),
    //   error:
    //       (error, stackTrace) => Padding(
    //         padding: EdgeInsets.symmetric(horizontal: 20),
    //         child: Center(
    //           child: Column(
    //             mainAxisAlignment: MainAxisAlignment.center,
    //             children: [
    //               Icon(
    //                 Icons.error_outline,
    //                 size: 48,
    //                 color: AppColors.blackColor.withOpacity(0.6),
    //               ),
    //               SizedBox(height: 16),
    //               OpenSans600Large(
    //                 14,
    //                 'Connection Error',
    //                 AppColors.blackColor.withOpacity(0.8),
    //               ),
    //               SizedBox(height: 8),
    //               OpenSans500Large(
    //                 12,
    //                 error.toString().contains('timeout')
    //                     ? 'Server timeout - trying smaller batch'
    //                     : 'Please check your connection',
    //                 AppColors.blackColor.withOpacity(0.6),
    //               ),
    //               SizedBox(height: 16),
    //               ElevatedButton(
    //                 onPressed: () => ref.refresh(allEmployeesProvider),
    //                 style: ElevatedButton.styleFrom(
    //                   backgroundColor: AppColors.blackColor,
    //                   foregroundColor: AppColors.whiteColor,
    //                   padding: EdgeInsets.symmetric(
    //                     horizontal: 24,
    //                     vertical: 12,
    //                   ),
    //                 ),
    //                 child: Text('Retry'),
    //               ),
    //             ],
    //           ),
    //         ),
    //       ),
    // );
  

  // Widget _buildDirectoryContent(List<Map<String, dynamic>> employees) {
  //   if (employees.isEmpty) {
  //     return Padding(
  //       padding: EdgeInsets.symmetric(horizontal: 20),
  //       child: Center(
  //         child: Column(
  //           mainAxisAlignment: MainAxisAlignment.center,
  //           children: [
  //             Icon(
  //               Icons.people_outline,
  //               size: 48,
  //               color: AppColors.blackColor.withOpacity(0.6),
  //             ),
  //             SizedBox(height: 16),
  //             OpenSans600Large(
  //               14,
  //               'No employees found',
  //               AppColors.blackColor.withOpacity(0.6),
  //             ),
  //           ],
  //         ),
  //       ),
  //     );
  //   }

  //   // Filter employees based on search query if provided
  //   List<Map<String, dynamic>> filteredEmployees = employees;
  //   if (widget.searchQuery != null && widget.searchQuery!.isNotEmpty) {
  //     final searchLower = widget.searchQuery!.toLowerCase();
  //     filteredEmployees =
  //         employees.where((employee) {
  //           final displayName =
  //               employee['DisplayName']?.toString().toLowerCase() ?? '';
  //           final workEmail =
  //               employee['WorkEmail']?.toString().toLowerCase() ?? '';
  //           final city = employee['City']?.toString().toLowerCase() ?? '';
  //           final addressLine1 =
  //               employee['AddressLine1']?.toString().toLowerCase() ?? '';
  //           final addressLine2 =
  //               employee['AddressLine2']?.toString().toLowerCase() ?? '';
  //           final addressLine3 =
  //               employee['AddressLine3']?.toString().toLowerCase() ?? '';

  //           return displayName.contains(searchLower) ||
  //               workEmail.contains(searchLower) ||
  //               city.contains(searchLower) ||
  //               addressLine1.contains(searchLower) ||
  //               addressLine2.contains(searchLower) ||
  //               addressLine3.contains(searchLower);
  //         }).toList();
  //   }

  //   // Sort employees by DisplayName
  //   final sortedPeople = List<Map<String, dynamic>>.from(filteredEmployees);
  //   sortedPeople.sort((a, b) {
  //     final nameA = a['DisplayName']?.toString() ?? '';
  //     final nameB = b['DisplayName']?.toString() ?? '';
  //     return nameA.compareTo(nameB);
  //   });

  //   // Group by first letter
  //   final Map<String, List<Map<String, dynamic>>> groupedPeople = {};
  //   for (var person in sortedPeople) {
  //     final displayName = person['DisplayName']?.toString() ?? '';
  //     String initial =
  //         displayName.isNotEmpty ? displayName[0].toUpperCase() : '#';
  //     groupedPeople.putIfAbsent(initial, () => []).add(person);
  //   }

  //   return Padding(
  //     padding: EdgeInsets.symmetric(horizontal: 20),
  //     child: Column(
  //       children: [
  //         Expanded(
  //           child: ListView(
  //             children:
  //                 groupedPeople.entries.map((entry) {
  //                   return Column(
  //                     crossAxisAlignment: CrossAxisAlignment.start,
  //                     children: [
  //                       OpenSans600Large(12, entry.key, AppColors.blackColor),
  //                       SizedBox(height: 8),
  //                       ...entry.value.asMap().entries.map((entryMap) {
  //                         final index = entryMap.key;
  //                         final person = entryMap.value;
  //                         final isLast = index == entry.value.length - 1;

  //                         return Column(
  //                           children: [
  //                             _buildPersonCard(context, person),
  //                             if (!isLast) SizedBox(height: 8),
  //                           ],
  //                         );
  //                       }),
  //                       SizedBox(height: 20),
  //                     ],
  //                   );
  //                 }).toList(),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

//   Widget _buildPersonCard(BuildContext context, Map<String, dynamic> person) {
//     final personId = person['PersonId']?.toString() ?? '';
//     bool isPressed = _pressedPersonId == personId;

//     // Extract data from API response
//     final displayName = person['DisplayName']?.toString() ?? 'Unknown';
//     final workEmail = person['WorkEmail']?.toString() ?? 'No email';
//     final city = person['City']?.toString() ?? '';
//     final addressLine1 = person['AddressLine1']?.toString() ?? '';
//     final addressLine2 = person['AddressLine2']?.toString() ?? '';
//     final addressLine3 = person['AddressLine3']?.toString() ?? '';

//     // Combine address parts (3,2,1) with commas
//     List<String> addressParts = [];
//     if (addressLine3.isNotEmpty) addressParts.add(addressLine3);
//     if (addressLine2.isNotEmpty) addressParts.add(addressLine2);
//     if (addressLine1.isNotEmpty) addressParts.add(addressLine1);
//     if (city.isNotEmpty) addressParts.add(city);

//     String location = '';
//     if (addressParts.isNotEmpty) {
//       location = addressParts.join(', ');
//       if (location.length > 40) {
//         location = location.substring(0, 37) + '...';
//       }
//     } else if (city.isNotEmpty) {
//       location = city;
//     } else {
//       location = 'No location';
//     }

//     return GestureDetector(
//       onTapDown: (_) {
//         setState(() {
//           _pressedPersonId = personId;
//         });
//       },
//       onTapUp: (_) {
//         setState(() {
//           _pressedPersonId = null;
//         });
//         context.push('/profile-info/$personId');
//       },
//       onTapCancel: () {
//         setState(() {
//           _pressedPersonId = null;
//         });
//       },
//       child: AnimatedContainer(
//         duration: Duration(milliseconds: 100),
//         height: 72,
//         margin: EdgeInsets.zero,
//         decoration: BoxDecoration(
//           color: isPressed ? AppColors.microinteraction : AppColors.whiteColor,
//           borderRadius: BorderRadius.circular(8),
//           boxShadow: [
//             BoxShadow(color: AppColors.peopleboxshadow, blurRadius: 9.6),
//           ],
//         ),
//         child: Row(
//           children: [
//             Padding(
//               padding: EdgeInsets.all(10),
//               child: CircleAvatar(
//                 radius: 26.5,
//                 child: Icon(Icons.person, size: 40),
//                 backgroundColor: Colors.grey[300],
//               ),
//             ),
//             Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 OpenSans600Large(14, displayName, AppColors.blackColor),
//                 SizedBox(height: 8),
//                 OpenSans400Large(12, location, AppColors.darkGreyColor),
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }




class _GroupItem {
  final bool isHeader;
  final String? header;
  final Map<String, dynamic>? person;

  _GroupItem.isHeader(this.header)
      : isHeader = true,
        person = null;

  _GroupItem.isPerson(this.person)
      : isHeader = false,
        header = null;
}