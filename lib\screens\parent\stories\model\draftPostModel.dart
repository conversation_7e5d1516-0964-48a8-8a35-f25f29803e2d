import 'dart:convert';

List<DraftedPostModel> draftPostModelFromJson(String str) =>
    List<DraftedPostModel>.from(json.decode(str).map((x) => DraftedPostModel.fromJson(x)));

String draftPostModelToJson(List<DraftedPostModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class DraftedPostModel {
  int? id;
  String? postBody;
  int? postType;
  int? postStatus;
  int? scope;
  bool? isPostFeatured;
  bool? isAllowedToComment;
  DateTime? createdDate;
  DateTime? eventStartDate;
  DateTime? eventEndDate;
  String? eventLocation;
  CreatedBy? createdBy;
  int? commentCount;
  int? reactionCount;
  int? viewCount;
  ReactionCountsByGroup? reactionCountsByGroup;
  List<PostAttachment>? postAttachments;
  List<dynamic>? postClasses;
  List<dynamic>? postNurseries;
  List<dynamic>? postClassSections;
  List<dynamic>? postStudentDetails;
  List<PostTag>? postTags;

  DraftedPostModel({
    this.id,
    this.postBody,
    this.postType,
    this.postStatus,
    this.scope,
    this.isPostFeatured,
    this.isAllowedToComment,
    this.createdDate,
    this.eventStartDate,
    this.eventEndDate,
    this.eventLocation,
    this.createdBy,
    this.commentCount,
    this.reactionCount,
    this.viewCount,
    this.reactionCountsByGroup,
    this.postAttachments,
    this.postClasses,
    this.postNurseries,
    this.postClassSections,
    this.postStudentDetails,
    this.postTags,
  });

  factory DraftedPostModel.fromJson(Map<String, dynamic> json) => DraftedPostModel(
        id: json["Id"],
        postBody: json["PostBody"],
        postType: json["PostType"],
        postStatus: json["PostStatus"],
        scope: json["Scope"],
        isPostFeatured: json["IsPostFeatured"],
        isAllowedToComment: json["IsAllowedToComment"],
        createdDate: json["CreatedDate"] == null ? null : DateTime.parse(json["CreatedDate"]),
        eventStartDate: json["EventStartDate"] == null ? null : DateTime.parse(json["EventStartDate"]),
        eventEndDate: json["EventEndDate"] == null ? null : DateTime.parse(json["EventEndDate"]),
        eventLocation: json["EventLocation"],
        createdBy: json["CreatedBy"] == null ? null : CreatedBy.fromJson(json["CreatedBy"]),
        commentCount: json["CommentCount"],
        reactionCount: json["ReactionCount"],
        viewCount: json["ViewCount"],
        reactionCountsByGroup: json["ReactionCountsByGroup"] == null 
            ? null 
            : ReactionCountsByGroup.fromJson(json["ReactionCountsByGroup"]),
        postAttachments: json["PostAttachments"] == null
            ? []
            : List<PostAttachment>.from(json["PostAttachments"].map((x) => PostAttachment.fromJson(x))),
        postClasses: json["PostClasses"] == null ? [] : List<dynamic>.from(json["PostClasses"]),
        postNurseries: json["PostNurseries"] == null ? [] : List<dynamic>.from(json["PostNurseries"]),
        postClassSections: json["PostClassSections"] == null 
            ? [] 
            : List<dynamic>.from(json["PostClassSections"]),
        postStudentDetails: json["PostStudentDetails"] == null 
            ? [] 
            : List<dynamic>.from(json["PostStudentDetails"]),
        postTags: json["PostTags"] == null
            ? []
            : List<PostTag>.from(json["PostTags"].map((x) => PostTag.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "Id": id,
        "PostBody": postBody,
        "PostType": postType,
        "PostStatus": postStatus,
        "Scope": scope,
        "IsPostFeatured": isPostFeatured,
        "IsAllowedToComment": isAllowedToComment,
        "CreatedDate": createdDate?.toIso8601String(),
        "EventStartDate": eventStartDate?.toIso8601String(),
        "EventEndDate": eventEndDate?.toIso8601String(),
        "EventLocation": eventLocation,
        "CreatedBy": createdBy?.toJson(),
        "CommentCount": commentCount,
        "ReactionCount": reactionCount,
        "ViewCount": viewCount,
        "ReactionCountsByGroup": reactionCountsByGroup?.toJson(),
        "PostAttachments": postAttachments == null 
            ? [] 
            : List<dynamic>.from(postAttachments!.map((x) => x.toJson())),
        "PostClasses": postClasses,
        "PostNurseries": postNurseries,
        "PostClassSections": postClassSections,
        "PostStudentDetails": postStudentDetails,
        "PostTags": postTags == null 
            ? [] 
            : List<dynamic>.from(postTags!.map((x) => x.toJson())),
      };

  // Static method for parsing list from JSON
  static List<DraftedPostModel> listFromJson(List<dynamic> jsonList) {
    return jsonList.map((json) => DraftedPostModel.fromJson(json)).toList();
  }
}

class CreatedBy {
  int? id;
  String? name;
  String? nameInArabic;
  String? role;
  List<AssignedNursery>? assignedNurseries;

  CreatedBy({
    this.id,
    this.name,
    this.nameInArabic,
    this.role,
    this.assignedNurseries,
  });

  factory CreatedBy.fromJson(Map<String, dynamic> json) => CreatedBy(
        id: json["Id"],
        name: json["Name"],
        nameInArabic: json["NameInArabic"],
        role: json["Role"],
        assignedNurseries: json["AssignedNurseries"] == null
            ? []
            : List<AssignedNursery>.from(
                json["AssignedNurseries"].map((x) => AssignedNursery.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "Id": id,
        "Name": name,
        "NameInArabic": nameInArabic,
        "Role": role,
        "AssignedNurseries": assignedNurseries == null
            ? []
            : List<dynamic>.from(assignedNurseries!.map((x) => x.toJson())),
      };
}

class AssignedNursery {
  int? id;
  String? name;
  String? nameInArabic;

  AssignedNursery({
    this.id,
    this.name,
    this.nameInArabic,
  });

  factory AssignedNursery.fromJson(Map<String, dynamic> json) => AssignedNursery(
        id: json["Id"],
        name: json["Name"],
        nameInArabic: json["NameInArabic"],
      );

  Map<String, dynamic> toJson() => {
        "Id": id,
        "Name": name,
        "NameInArabic": nameInArabic,
      };
}

class ReactionCountsByGroup {
  int? likeCount;
  int? heartCount;
  int? clapsCount;
  int? sadCount;

  ReactionCountsByGroup({
    this.likeCount,
    this.heartCount,
    this.clapsCount,
    this.sadCount,
  });

  factory ReactionCountsByGroup.fromJson(Map<String, dynamic> json) => ReactionCountsByGroup(
        likeCount: json["LikeCount"],
        heartCount: json["HeartCount"],
        clapsCount: json["ClapsCount"],
        sadCount: json["SadCount"],
      );

  Map<String, dynamic> toJson() => {
        "LikeCount": likeCount,
        "HeartCount": heartCount,
        "ClapsCount": clapsCount,
        "SadCount": sadCount,
      };

  ReactionCountsByGroup copyWith({
    int? likeCount,
    int? heartCount,
    int? clapsCount,
    int? sadCount,
  }) {
    return ReactionCountsByGroup(
      likeCount: likeCount ?? this.likeCount,
      heartCount: heartCount ?? this.heartCount,
      clapsCount: clapsCount ?? this.clapsCount,
      sadCount: sadCount ?? this.sadCount,
    );
  }
}

class PostAttachment {
  int? id;
  int? postId;
  String? attachmentKey;
  String? thumbnailKey;
  int? attachmentType;

  PostAttachment({
    this.id,
    this.postId,
    this.attachmentKey,
    this.thumbnailKey,
    this.attachmentType,
  });

  factory PostAttachment.fromJson(Map<String, dynamic> json) => PostAttachment(
        id: json["Id"],
        postId: json["PostId"],
        attachmentKey: json["AttachmentKey"],
        thumbnailKey: json["ThumbnailKey"],
        attachmentType: json["AttachmentType"],
      );

  Map<String, dynamic> toJson() => {
        "Id": id,
        "PostId": postId,
        "AttachmentKey": attachmentKey,
        "ThumbnailKey": thumbnailKey,
        "AttachmentType": attachmentType,
      };
}

class PostTag {
  int? id;
  int? postId;
  int? userId;
  int? userType;
  TaggedUser? taggedUser;

  PostTag({
    this.id,
    this.postId,
    this.userId,
    this.userType,
    this.taggedUser,
  });

  factory PostTag.fromJson(Map<String, dynamic> json) => PostTag(
        id: json["Id"],
        postId: json["PostId"],
        userId: json["UserId"],
        userType: json["UserType"],
        taggedUser: json["TaggedUser"] == null 
            ? null 
            : TaggedUser.fromJson(json["TaggedUser"]),
      );

  Map<String, dynamic> toJson() => {
        "Id": id,
        "PostId": postId,
        "UserId": userId,
        "UserType": userType,
        "TaggedUser": taggedUser?.toJson(),
      };
}

class TaggedUser {
  int? id;
  String? name;
  String? nameInArabic;
  String? role;

  TaggedUser({
    this.id,
    this.name,
    this.nameInArabic,
    this.role,
  });

  factory TaggedUser.fromJson(Map<String, dynamic> json) => TaggedUser(
        id: json["Id"],
        name: json["Name"],
        nameInArabic: json["NameInArabic"],
        role: json["Role"],
      );

  Map<String, dynamic> toJson() => {
        "Id": id,
        "Name": name,
        "NameInArabic": nameInArabic,
        "Role": role,
      };
}
