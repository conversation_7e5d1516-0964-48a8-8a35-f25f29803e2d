import 'package:flutter/material.dart';
import 'package:seawork/utils/downloadUtils.dart';
import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import '../model/storiesListModel.dart';
import '../model/tag_profile.dart';
import '../model/nursery_class_section.dart';
import '../model/student_autocomplete.dart';
import '../repository/storiesRepository.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/parent/parent/repository/parentRepository.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import '../model/draftPostModel.dart' hide ReactionCountsByGroup;


class StoriesProvider extends ChangeNotifier {
  final StoriesRepository _repository;
  final ParentRepository _parentRepository;

  // Store posts in a Map by ID for more robust updates
  final Map<int, StoriesListModel> _byId = {};
  // Keep track of post order
  List<int> _postIds = [];

  List<DraftedPostModel> _draftedPosts = [];
  bool _isLoadingDrafts = false;
  String _draftsError = '';

  int _currentPage = 1;
  final int _pageSize = 10;
  bool _isLoading = false;
  bool _hasMore = true;
  String _orderBy = 'desc';
  int _userType = 1;
  bool _isForYou = false;                 // <─ NEW

  // Caches for presigned URLs
  final Map<String, String> _profilePicUrlCache = {};
  final Map<String, String> _postImageUrlCache = {};
  
  // Shield for optimistic updates
  final Map<int, StoriesListModel> _localOverrides = {};

  // ─────────────────────────────────────────────
  //  ➜ 1.  Add fields for post view tracking
  // ─────────────────────────────────────────────
  final Set<int> _viewedPostIds = {};              // one-per-session rule
  final List<PostViewPayload> _pendingViews = []; // waiting to flush
  Timer? _flushTimer;
  
  // Getter that derives a list from the map for UI consumption
  List<StoriesListModel> get posts {
    // ① start with the order we already keep
    final list = _postIds.map((id) => _byId[id]!).toList();
    // ② patch each element if we have an override
    return list.map((p) => _localOverrides[p.id] ?? p).toList();
  }
  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  int get userType => _userType;
  bool get isForYou => _isForYou;

  List<DraftedPostModel> get draftedPosts => _draftedPosts;
  bool get isLoadingDrafts => _isLoadingDrafts;
  String get draftsError => _draftsError;

  StoriesProvider({
    StoriesRepository? repository,
    ParentRepository? parentRepository,
  })  : _repository = repository ?? StoriesRepository(baseUrl: baseUrlNMSProvider),
        _parentRepository = parentRepository ?? ParentRepository(Dio());

  Future<void> fetchDraftedPosts({
    required int userId,
    int pageNumber = 1,
    int pageSize = 15,
    String orderBy = 'desc',
    int userType = 1,
  }) async {
    _isLoadingDrafts = true;
    _draftsError = '';
    notifyListeners();
    try {
      final drafts = await _repository.getAllDraftedPosts(
        pageNumber: pageNumber,
        pageSize: pageSize,
        orderBy: orderBy,
        userType: userType,
      );
      _draftedPosts = drafts;
    } catch (e) {
      _draftsError = e.toString();
      _draftedPosts = [];
    } finally {
      _isLoadingDrafts = false;
      notifyListeners();
    }
  }

  // ---------------------------------
  // TOP: new helper to clear caches
  // ---------------------------------
  void reset() {
    _byId.clear();          // global array == empty
    _postIds.clear();
    _localOverrides.clear();
    _currentPage = 1;
    _hasMore = true;
    notifyListeners();
  }

  // ---------------------------------
  // fetchPosts()  – pagination append
  // ---------------------------------
  Future<void> fetchPosts({
    bool refresh = false,
    bool forYou = false,                 // <─ NEW
  }) async {
    if (_isLoading) return;
    _isLoading = true;
    _isForYou = forYou;                  // remember which feed we are on
    
    if (refresh) {
      reset();                      // ① wipe local array on full refresh
    }

    notifyListeners();
    try {
      final newPosts = forYou
          ? await _repository.getAllPostsForYou(
              pageNumber: _currentPage,
              pageSize  : _pageSize,
              orderBy   : _orderBy,
              userType  : _userType,
            )
          : await _repository.getAllPosts(page: _currentPage);
          
      for (final p in newPosts) {
        if (p.id == null) continue;
        _byId[p.id!] = p;           // ② ADD / UPDATE single post
        if (!_postIds.contains(p.id)) _postIds.add(p.id!);
      }
      _hasMore = newPosts.length == _pageSize;
      if (_hasMore) _currentPage++;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get profile picture URL for a post's CreatedBy.ProfileDocumentUpload
  Future<String?> getProfilePicUrl(String? key) async {
    debugPrint('🚨 OLD getProfilePicUrl called with key: $key');
    if (key == null || key.isEmpty) return null;
    if (_profilePicUrlCache.containsKey(key)) {
      return _profilePicUrlCache[key];
    }
    final url = await _repository.getPresignedUrl(key);
    if (url != null) {
      _profilePicUrlCache[key] = url;
    }
    return url;
  }

  // Get post image URL for a post's first PostAttachments.attachmentKey
  Future<String?> getPostImageUrl(String? key) async {
    if (key == null || key.isEmpty) return null;
    if (_postImageUrlCache.containsKey(key)) {
      return _postImageUrlCache[key];
    }
    final url = await _repository.getPresignedUrl(key);
    if (url != null) {
      _postImageUrlCache[key] = url;
    }
    return url;
  }

  Future<Uint8List?> getImageBytes(String? key) async {
    debugPrint('🔍 getImageBytes called with key: $key');
    if (key == null || key.isEmpty) return null;

    final cacheKey = '${key}_bytes';
    if (_postImageUrlCache.containsKey(cacheKey)) {
      debugPrint('🔍 Found binary data in cache');
      final cachedDataUrl = _postImageUrlCache[cacheKey]!;
      final base64Data = cachedDataUrl.split(',')[1];
      return base64Decode(base64Data);
    }

    debugPrint('🔍 Calling _repository.fetchPresignedUrl...');
    final dataUrl = await _repository.fetchPresignedUrl(key);
    debugPrint('🔍 fetchPresignedUrl returned data URL: ${dataUrl?.substring(0, 50)}...');

    if (dataUrl != null && dataUrl.startsWith('data:image/')) {
      debugPrint('✅ Valid data URL received');
      _postImageUrlCache[cacheKey] = dataUrl;

      final base64Data = dataUrl.split(',')[1];
      final bytes = base64Decode(base64Data);
      debugPrint('✅ Converted to ${bytes.length} bytes');
      return bytes;
    } else {
      debugPrint('❌ Invalid or null data URL');
      return null;
    }
  }

  @Deprecated('Use getImageBytes() instead for better performance')
  Future<String?> getPresignedUrlNew(String? key) async {
    final bytes = await getImageBytes(key);
    if (bytes != null) {
      final base64String = base64Encode(bytes);
      return 'data:image/jpeg;base64,$base64String'; // Default to JPEG
    }
    return null;
  }

  // ---------------------------------
  // reactToPost()  – fire-and-forget
  // ---------------------------------
  Future<void> reactToPost({
    required int postId,
    required int reactionType,
    String? imageKey, // NEW: optional image key for presigned URL
  }) async {
    if (!_byId.containsKey(postId)) return;

    final original = _byId[postId]!;
    final oldType  = original.userReaction?.reactionType ?? ReactionType.none;
    final bool isOff = oldType == reactionType;

    // 1⃣ optimistic patch (unchanged)
    final int newType = isOff ? ReactionType.none : reactionType;
    final updated     = _applyLocalReaction(original, newType);
    _byId[postId]         = updated;
    _localOverrides[postId] = updated;          // ⬅️ keep it until refresh
    notifyListeners();

    // 2⃣ background call – NO await, NO rollback, just log
    _repository.addOrUpdatePostReaction(
      postId      : postId,
      userType    : _userType,
      reactionType: reactionType,
      imageKey    : imageKey, // NEW: pass image key
    ).then((ok) {
      if (!ok) debugPrint('❌ react API failed (ignored by UI)');
    }).catchError((error) {
      debugPrint('❌ react API error: $error');
    });
  }

  // ---------------------------------------------
  // REPORT / FLAG a post
  // ---------------------------------------------
  Future<void> reportPost({
    required int postId,
    required int reasonType,   // 1-based index that matches the API
    required String remarks,
    String? imageKey, // NEW: optional image key for presigned URL
  }) async {
    // optimistic-UI: immediately mark the post locally (optional)
    if (_byId[postId] != null) {
      _localOverrides[postId] = _byId[postId]!
          .copyWith(postStatus: 999); // 999 = "flagged locally"
      notifyListeners();
    }

    final ok = await _repository.addOrRemovePostFlags(
      postId     : postId,
      userType   : _userType,
      reasonType : reasonType,
      description: remarks,
      imageKey   : imageKey, // NEW: pass image key
    );

    if (ok) {
      // you might fetch the post again or show a SnackBar
    } else {
      // roll back
      _localOverrides.remove(postId);
      notifyListeners();
    }
  }

  Future<bool> hidePostFromFeed({
    required int postId,
    String? imageKey, 
  }) async {
    if (_byId.containsKey(postId)) {
      _byId.remove(postId);
      _postIds.remove(postId);
      _localOverrides.remove(postId); 
      notifyListeners();
    }

    _repository.addOrRemovePostFlags(
      postId     : postId,
      userType   : _userType,
      reasonType : 9,                           
      description: 'Hide from feed',          
      imageKey   : imageKey,
    ).then((ok) {
      if (!ok) {
        debugPrint('❌ hide from feed API failed (post already removed from UI)');
      } else {
        debugPrint('✅ hide from feed API succeeded');
      }
    }).catchError((error) {
      debugPrint('❌ hide from feed API error: $error');
    });

    return true;
  }

  // -------------------------------------------------------------------------
  // internal helper – returns a NEW model with counters adjusted
  // -------------------------------------------------------------------------
  StoriesListModel _applyLocalReaction(StoriesListModel story, int newType) {
    final oldType = story.userReaction?.reactionType ?? ReactionType.none;
    if (oldType == newType) return story;   // nothing to change

    ReactionCountsByGroup counts = (story.reactionCountsByGroup ??
            ReactionCountsByGroup(
              heartCount: 0,
              likeCount: 0,
              clapsCount: 0,
              sadCount:  0,
            ))
        .copyWith(); // clone

    void dec(int t) {
      switch (t) {
        case ReactionType.thumbsup: counts = counts.copyWith(likeCount: (counts.likeCount ?? 1) - 1); break;
        case ReactionType.heart:    counts = counts.copyWith(heartCount: (counts.heartCount ?? 1) - 1); break;
        case ReactionType.clap:     counts = counts.copyWith(clapsCount: (counts.clapsCount ?? 1) - 1); break;
        case ReactionType.smile:    counts = counts.copyWith(sadCount: (counts.sadCount ?? 1) - 1); break;
      }
    }
    void inc(int t) {
      switch (t) {
        case ReactionType.thumbsup: counts = counts.copyWith(likeCount: (counts.likeCount ?? 0) + 1); break;
        case ReactionType.heart:    counts = counts.copyWith(heartCount: (counts.heartCount ?? 0) + 1); break;
        case ReactionType.clap:     counts = counts.copyWith(clapsCount: (counts.clapsCount ?? 0) + 1); break;
        case ReactionType.smile:    counts = counts.copyWith(sadCount: (counts.sadCount ?? 0) + 1); break;
      }
    }
    if (oldType != ReactionType.none) dec(oldType);
    if (newType != ReactionType.none) inc(newType);

    return story.copyWith(
      reactionCountsByGroup: counts,
      userReaction: newType == ReactionType.none
          ? null
          : (story.userReaction?.copyWith(reactionType: newType) ??
              UserReaction(
                postId: story.id,
                userId: null, // Backend handles user identification
                userType: _userType,
                reactionType: newType,
              )),
    );
  }

  // ─────────────────────────────────────────────
  //  ➜ 2.  Public helper – call from UI
  // ─────────────────────────────────────────────
  void markPostViewed(int postId) {
    // Rule: same user can view a post only once in this session
    if (_viewedPostIds.contains(postId)) return;

    _viewedPostIds.add(postId);
    _pendingViews.add(
      PostViewPayload(
        postId: postId,
        userId: 0, // Backend handles user identification
        userType: _userType,
      ),
    );
    _scheduleFlush();
  }

  // ─────────────────────────────────────────────
  //  ➜ 3.  Internal: debounce + flush
  // ─────────────────────────────────────────────
  void _scheduleFlush() {
    _flushTimer?.cancel();
    _flushTimer = Timer(const Duration(seconds: 5), _flushPendingViews);
  }

  Future<void> _flushPendingViews() async {
    if (_pendingViews.isEmpty) return;

    // copy ‑ then clear queue immediately (optimistic)
    final batch = List<PostViewPayload>.from(_pendingViews);
    _pendingViews.clear();

    final ok = await _repository.addMultiplePostViews(batch);
    if (!ok) {
      // put them back so we can retry on the next flush
      _pendingViews.insertAll(0, batch);
      // optionally: exponential-back-off, but simple retry next 10 s
      _flushTimer ??= Timer(const Duration(seconds: 10), _flushPendingViews);
    }
  }

  // ---------------- FETCH-ON-ENTRY DATA -----------------
  List<TagProfile> _tagProfiles = [];
  List<NurseryClassSection> _nurseryTree = [];
  List<AutocompleteStudent> _students = [];
  
  bool _loadingTagProfiles = false;
  bool _loadingNurseryTree = false;
  bool _loadingStudents = false;
  
  bool _tagProfilesLoaded = false;
  bool _nurseryTreeLoaded = false;
  bool _studentsLoaded = false;

  List<TagProfile> get tagProfiles => _tagProfiles;
  List<NurseryClassSection> get nurseryTree => _nurseryTree;
  List<AutocompleteStudent> get studentsList => _students;
  
  bool get loadingTagProfiles => _loadingTagProfiles;
  bool get loadingNurseryTree => _loadingNurseryTree;
  bool get loadingStudents => _loadingStudents;
  
  bool get loadingMeta => _loadingTagProfiles || _loadingNurseryTree || _loadingStudents;

  Future<void> loadCreatePostMeta() async {
    await Future.wait([
      loadTagProfiles(),
      loadNurseryClassSections(),
      loadStudentsForParent(),
    ]);
  }

  Future<void> loadTagProfiles() async {
    if (_loadingTagProfiles || _tagProfilesLoaded) return;
    
    _loadingTagProfiles = true;
    notifyListeners();
    
    try {
      debugPrint('🔄 Loading tag profiles...');
      _tagProfiles = await _repository.fetchTagProfiles().catchError((e) {
        debugPrint('fetchTagProfiles failed: $e');
        return <TagProfile>[];
      });
      _tagProfilesLoaded = true;
      debugPrint('✅ Tag profiles loaded: ${_tagProfiles.length} items');
    } catch (e) {
      debugPrint('loadTagProfiles failed: $e');
      _tagProfiles = [];
    } finally {
      _loadingTagProfiles = false;
      notifyListeners();
    }
  }

  Future<void> loadNurseryClassSections() async {
    if (_loadingNurseryTree || _nurseryTreeLoaded) return;
    
    _loadingNurseryTree = true;
    notifyListeners();
    
    try {
      debugPrint('🔄 Loading nursery class sections...');
      _nurseryTree = await _repository.fetchNurseryClassSections().catchError((e) {
        debugPrint('fetchNurseryClassSections failed: $e');
        return <NurseryClassSection>[];
      });
      _nurseryTreeLoaded = true;
      debugPrint('✅ Nursery class sections loaded: ${_nurseryTree.length} items');
    } catch (e) {
      debugPrint('loadNurseryClassSections failed: $e');
      _nurseryTree = [];
    } finally {
      _loadingNurseryTree = false;
      notifyListeners();
    }
  }

  Future<void> loadStudentsForParent() async {
    if (_loadingStudents || _studentsLoaded) return;
    
    _loadingStudents = true;
    notifyListeners();
    
    try {
      debugPrint('🔄 Loading students for parent...');
      _students = await _repository.fetchStudentsForParent().catchError((e) {
        debugPrint('fetchStudentsForParent failed: $e');
        return <AutocompleteStudent>[];
      });
      _studentsLoaded = true;
      debugPrint('✅ Students for parent loaded: ${_students.length} items');
    } catch (e) {
      debugPrint('loadStudentsForParent failed: $e');
      _students = [];
    } finally {
      _loadingStudents = false;
      notifyListeners();
    }
  }

  Future<void> refreshCreatePostMeta() async {
    _tagProfilesLoaded = false;
    _nurseryTreeLoaded = false;
    _studentsLoaded = false;
    
    _tagProfiles.clear();
    _nurseryTree.clear();
    _students.clear();
    
    await loadCreatePostMeta();
  }

  // ---------------- DRAFT / PUBLISH STATE ---------------
  Set<int> selectedTagUserIds = {};
  Set<int> selectedStudentIds = {};
  Set<int> selectedNurseryIds = {};      // for audience logic
  bool allowComments = true;
  File? pickedImage;                     // set by UI
  String postBody = '';

  void toggleTag(int userId) {
    if (selectedTagUserIds.contains(userId)) {
      selectedTagUserIds.remove(userId);
    } else {
      selectedTagUserIds.add(userId);
    }
    notifyListeners();
  }

  void toggleStudent(int studentId) {
    if (selectedStudentIds.contains(studentId)) {
      selectedStudentIds.remove(studentId);
    } else {
      selectedStudentIds.add(studentId);
    }
    notifyListeners();
  }

  void setSelectedNurseries(List<int> nurseryIds) {
    selectedNurseryIds = nurseryIds.toSet();
    notifyListeners();
  }

  void toggleNursery(int nurseryId) {
    if (selectedNurseryIds.contains(nurseryId)) {
      selectedNurseryIds.remove(nurseryId);
    } else {
      selectedNurseryIds.add(nurseryId);
    }
    notifyListeners();
  }

  void setAllowComments(bool v) {
    allowComments = v;
    notifyListeners();
  }

  void setPostBody(String body) {
    postBody = body;
    notifyListeners();
  }

  void setImage(File? f) {
    pickedImage = f;
    notifyListeners();
  }

  /// High-level publish
  Future<bool> publishPost({
    required int parentId,
    required int userType,            // 1 = parent
    required int academicYearId,
  }) async {
    // 1. OPTIONAL image upload
    String? fileKey, thumbKey;
    if (pickedImage != null) {
      try {
        final platformFile = PlatformFile(
          name: pickedImage!.path.split('/').last,
          path: pickedImage!.path,
          size: await pickedImage!.length(),
        );
        final uploadResult = await _parentRepository.uploadFileToS3(platformFile);
        fileKey = uploadResult['fileKeyName'] as String?;
        thumbKey = uploadResult['thumbnailKey'] as String?;
        if (fileKey == null) return false;
      } catch (e) {
        debugPrint('uploadFileToS3 failed: $e');
        return false;
      }
    }

    // 2. Build payload
    final body = <String, dynamic>{
      'userId': parentId,
      'userType': userType,
      'postBody': postBody,
      'location': '',
      'scope': 1,
      'status': 1,
      'academicYearId': academicYearId,
      'postAttachments': fileKey == null
          ? []
          : [
              {
                'attachmentKey': fileKey,
                'thumbnailKey': thumbKey,
                'attachmentType': 1, // 1 = image
              }
            ],
      'postStudentDetails': selectedStudentIds
          .map((e) => {'Id': null, 'studentId': e}).toList(),
      'postTags': selectedTagUserIds
          .map((e) => {
                'Id': null,
                'userType': 3, // because our tags are currently students
                'userId': e
              })
          .toList(),
      'postNurseries': selectedNurseryIds
          .map((nurseryId) => {'nurseryGradeId': nurseryId}).toList(),
      'postClasses': [],
      'postClassSections': [],
      'postType': 1,
      'publishedDate': null,
      'isAllowedToComment': allowComments,
      'isPostFeatured': false,
    };

    // 3. POST /CreatePost
    final ok = await _repository.createPost(body);
    if (ok) _resetDraft();
    return ok;
  }

  void _resetDraft() {
    selectedTagUserIds.clear();
    selectedStudentIds.clear();
    selectedNurseryIds.clear();
    pickedImage = null;
    postBody = '';
    allowComments = true;
    notifyListeners();
  }
  Future<bool> downloadAttachment(String? fileKey) async {
    if (fileKey == null || fileKey.isEmpty) return false;
    try {
      final bytes = await _repository.fetchPresignedUrlDownload(fileKey);
      if (bytes == null) return false;
      final fileName = fileKey.split('/').lastOrNull ?? 'attachment';
      await DownloadUtilsForBytes.saveAsBytes(bytes, fileName: fileName);
      return true;
    } catch (e) {
      debugPrint('downloadAttachment failed: $e');
      return false;
    }
  }
  // ─────────────────────────────────────────────
  //  ➜ 4.  Clean-up
  // ─────────────────────────────────────────────
  @override
  void dispose() {
    _flushTimer?.cancel();
    super.dispose();
  }
}import 'package:flutter/material.dart';
import 'package:seawork/utils/downloadUtils.dart';
import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import '../model/storiesListModel.dart';
import '../model/tag_profile.dart';
import '../model/nursery_class_section.dart';
import '../model/student_autocomplete.dart';
import '../repository/storiesRepository.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/parent/parent/repository/parentRepository.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import '../model/draftPostModel.dart' ;


class StoriesProvider extends ChangeNotifier {
  final StoriesRepository _repository;
  final ParentRepository _parentRepository;

  // Store posts in a Map by ID for more robust updates
  final Map<int, StoriesListModel> _byId = {};
  // Keep track of post order
  List<int> _postIds = [];

  List<DraftedPostModel> _draftedPosts = [];
  bool _isLoadingDrafts = false;
  String _draftsError = '';

  int _currentPage = 1;
  final int _pageSize = 10;
  bool _isLoading = false;
  bool _hasMore = true;
  String _orderBy = 'desc';
  int _userType = 1;
  bool _isForYou = false;                 // <─ NEW

  // Caches for presigned URLs
  final Map<String, String> _profilePicUrlCache = {};
  final Map<String, String> _postImageUrlCache = {};
  
  // Shield for optimistic updates
  final Map<int, StoriesListModel> _localOverrides = {};

  // ─────────────────────────────────────────────
  //  ➜ 1.  Add fields for post view tracking
  // ─────────────────────────────────────────────
  final Set<int> _viewedPostIds = {};              // one-per-session rule
  final List<PostViewPayload> _pendingViews = []; // waiting to flush
  Timer? _flushTimer;
  
  // Getter that derives a list from the map for UI consumption
  List<StoriesListModel> get posts {
    // ① start with the order we already keep
    final list = _postIds.map((id) => _byId[id]!).toList();
    // ② patch each element if we have an override
    return list.map((p) => _localOverrides[p.id] ?? p).toList();
  }
  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  int get userType => _userType;
  bool get isForYou => _isForYou;

  List<DraftedPostModel> get draftedPosts => _draftedPosts;
  bool get isLoadingDrafts => _isLoadingDrafts;
  String get draftsError => _draftsError;

  StoriesProvider({
    StoriesRepository? repository,
    ParentRepository? parentRepository,
  })  : _repository = repository ?? StoriesRepository(baseUrl: baseUrlNMSProvider),
        _parentRepository = parentRepository ?? ParentRepository(Dio());

  Future<void> fetchDraftedPosts({
    required int userId,
    int pageNumber = 1,
    int pageSize = 15,
    String orderBy = 'desc',
    int userType = 1,
  }) async {
    _isLoadingDrafts = true;
    _draftsError = '';
    notifyListeners();
    try {
      final drafts = await _repository.getAllDraftedPosts(
        pageNumber: pageNumber,
        pageSize: pageSize,
        orderBy: orderBy,
        userType: userType,
      );
      _draftedPosts = drafts;
    } catch (e) {
      _draftsError = e.toString();
      _draftedPosts = [];
    } finally {
      _isLoadingDrafts = false;
      notifyListeners();
    }
  }

  // ---------------------------------
  // TOP: new helper to clear caches
  // ---------------------------------
  void reset() {
    _byId.clear();          // global array == empty
    _postIds.clear();
    _localOverrides.clear();
    _currentPage = 1;
    _hasMore = true;
    notifyListeners();
  }

  // ---------------------------------
  // fetchPosts()  – pagination append
  // ---------------------------------
  Future<void> fetchPosts({
    bool refresh = false,
    bool forYou = false,                 // <─ NEW
  }) async {
    if (_isLoading) return;
    _isLoading = true;
    _isForYou = forYou;                  // remember which feed we are on
    
    if (refresh) {
      reset();                      // ① wipe local array on full refresh
    }

    notifyListeners();
    try {
      final newPosts = forYou
          ? await _repository.getAllPostsForYou(
              pageNumber: _currentPage,
              pageSize  : _pageSize,
              orderBy   : _orderBy,
              userType  : _userType,
            )
          : await _repository.getAllPosts(page: _currentPage);
          
      for (final p in newPosts) {
        if (p.id == null) continue;
        _byId[p.id!] = p;           // ② ADD / UPDATE single post
        if (!_postIds.contains(p.id)) _postIds.add(p.id!);
      }
      _hasMore = newPosts.length == _pageSize;
      if (_hasMore) _currentPage++;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get profile picture URL for a post's CreatedBy.ProfileDocumentUpload
  Future<String?> getProfilePicUrl(String? key) async {
    debugPrint('🚨 OLD getProfilePicUrl called with key: $key');
    if (key == null || key.isEmpty) return null;
    if (_profilePicUrlCache.containsKey(key)) {
      return _profilePicUrlCache[key];
    }
    final url = await _repository.getPresignedUrl(key);
    if (url != null) {
      _profilePicUrlCache[key] = url;
    }
    return url;
  }

  // Get post image URL for a post's first PostAttachments.attachmentKey
  Future<String?> getPostImageUrl(String? key) async {
    if (key == null || key.isEmpty) return null;
    if (_postImageUrlCache.containsKey(key)) {
      return _postImageUrlCache[key];
    }
    final url = await _repository.getPresignedUrl(key);
    if (url != null) {
      _postImageUrlCache[key] = url;
    }
    return url;
  }

  Future<Uint8List?> getImageBytes(String? key) async {
    debugPrint('🔍 getImageBytes called with key: $key');
    if (key == null || key.isEmpty) return null;

    final cacheKey = '${key}_bytes';
    if (_postImageUrlCache.containsKey(cacheKey)) {
      debugPrint('🔍 Found binary data in cache');
      final cachedDataUrl = _postImageUrlCache[cacheKey]!;
      final base64Data = cachedDataUrl.split(',')[1];
      return base64Decode(base64Data);
    }

    debugPrint('🔍 Calling _repository.fetchPresignedUrl...');
    final dataUrl = await _repository.fetchPresignedUrl(key);
    debugPrint('🔍 fetchPresignedUrl returned data URL: ${dataUrl?.substring(0, 50)}...');

    if (dataUrl != null && dataUrl.startsWith('data:image/')) {
      debugPrint('✅ Valid data URL received');
      _postImageUrlCache[cacheKey] = dataUrl;

      final base64Data = dataUrl.split(',')[1];
      final bytes = base64Decode(base64Data);
      debugPrint('✅ Converted to ${bytes.length} bytes');
      return bytes;
    } else {
      debugPrint('❌ Invalid or null data URL');
      return null;
    }
  }

  @Deprecated('Use getImageBytes() instead for better performance')
  Future<String?> getPresignedUrlNew(String? key) async {
    final bytes = await getImageBytes(key);
    if (bytes != null) {
      final base64String = base64Encode(bytes);
      return 'data:image/jpeg;base64,$base64String'; // Default to JPEG
    }
    return null;
  }

  // ---------------------------------
  // reactToPost()  – fire-and-forget
  // ---------------------------------
  Future<void> reactToPost({
    required int postId,
    required int reactionType,
    String? imageKey, // NEW: optional image key for presigned URL
  }) async {
    if (!_byId.containsKey(postId)) return;

    final original = _byId[postId]!;
    final oldType  = original.userReaction?.reactionType ?? ReactionType.none;
    final bool isOff = oldType == reactionType;

    // 1⃣ optimistic patch (unchanged)
    final int newType = isOff ? ReactionType.none : reactionType;
    final updated     = _applyLocalReaction(original, newType);
    _byId[postId]         = updated;
    _localOverrides[postId] = updated;          // ⬅️ keep it until refresh
    notifyListeners();

    // 2⃣ background call – NO await, NO rollback, just log
    _repository.addOrUpdatePostReaction(
      postId      : postId,
      userType    : _userType,
      reactionType: reactionType,
      imageKey    : imageKey, // NEW: pass image key
    ).then((ok) {
      if (!ok) debugPrint('❌ react API failed (ignored by UI)');
    }).catchError((error) {
      debugPrint('❌ react API error: $error');
    });
  }

  // ---------------------------------------------
  // REPORT / FLAG a post
  // ---------------------------------------------
  Future<void> reportPost({
    required int postId,
    required int reasonType,   // 1-based index that matches the API
    required String remarks,
    String? imageKey, // NEW: optional image key for presigned URL
  }) async {
    // optimistic-UI: immediately mark the post locally (optional)
    if (_byId[postId] != null) {
      _localOverrides[postId] = _byId[postId]!
          .copyWith(postStatus: 999); // 999 = "flagged locally"
      notifyListeners();
    }

    final ok = await _repository.addOrRemovePostFlags(
      postId     : postId,
      userType   : _userType,
      reasonType : reasonType,
      description: remarks,
      imageKey   : imageKey, // NEW: pass image key
    );

    if (ok) {
      // you might fetch the post again or show a SnackBar
    } else {
      // roll back
      _localOverrides.remove(postId);
      notifyListeners();
    }
  }

  Future<bool> hidePostFromFeed({
    required int postId,
    String? imageKey, 
  }) async {
    if (_byId.containsKey(postId)) {
      _byId.remove(postId);
      _postIds.remove(postId);
      _localOverrides.remove(postId); 
      notifyListeners();
    }

    _repository.addOrRemovePostFlags(
      postId     : postId,
      userType   : _userType,
      reasonType : 9,                           
      description: 'Hide from feed',          
      imageKey   : imageKey,
    ).then((ok) {
      if (!ok) {
        debugPrint('❌ hide from feed API failed (post already removed from UI)');
      } else {
        debugPrint('✅ hide from feed API succeeded');
      }
    }).catchError((error) {
      debugPrint('❌ hide from feed API error: $error');
    });

    return true;
  }

  // -------------------------------------------------------------------------
  // internal helper – returns a NEW model with counters adjusted
  // -------------------------------------------------------------------------
  StoriesListModel _applyLocalReaction(StoriesListModel story, int newType) {
    final oldType = story.userReaction?.reactionType ?? ReactionType.none;
    if (oldType == newType) return story;   // nothing to change

    ReactionCountsByGroup counts = (story.reactionCountsByGroup ??
            ReactionCountsByGroup(
              heartCount: 0,
              likeCount: 0,
              clapsCount: 0,
              sadCount:  0,
            ))
        .copyWith(); // clone

    void dec(int t) {
      switch (t) {
        case ReactionType.thumbsup: counts = counts.copyWith(likeCount: (counts.likeCount ?? 1) - 1); break;
        case ReactionType.heart:    counts = counts.copyWith(heartCount: (counts.heartCount ?? 1) - 1); break;
        case ReactionType.clap:     counts = counts.copyWith(clapsCount: (counts.clapsCount ?? 1) - 1); break;
        case ReactionType.smile:    counts = counts.copyWith(sadCount: (counts.sadCount ?? 1) - 1); break;
      }
    }
    void inc(int t) {
      switch (t) {
        case ReactionType.thumbsup: counts = counts.copyWith(likeCount: (counts.likeCount ?? 0) + 1); break;
        case ReactionType.heart:    counts = counts.copyWith(heartCount: (counts.heartCount ?? 0) + 1); break;
        case ReactionType.clap:     counts = counts.copyWith(clapsCount: (counts.clapsCount ?? 0) + 1); break;
        case ReactionType.smile:    counts = counts.copyWith(sadCount: (counts.sadCount ?? 0) + 1); break;
      }
    }
    if (oldType != ReactionType.none) dec(oldType);
    if (newType != ReactionType.none) inc(newType);

    return story.copyWith(
      reactionCountsByGroup: counts,
      userReaction: newType == ReactionType.none
          ? null
          : (story.userReaction?.copyWith(reactionType: newType) ??
              UserReaction(
                postId: story.id,
                userId: null, // Backend handles user identification
                userType: _userType,
                reactionType: newType,
              )),
    );
  }

  // ─────────────────────────────────────────────
  //  ➜ 2.  Public helper – call from UI
  // ─────────────────────────────────────────────
  void markPostViewed(int postId) {
    // Rule: same user can view a post only once in this session
    if (_viewedPostIds.contains(postId)) return;

    _viewedPostIds.add(postId);
    _pendingViews.add(
      PostViewPayload(
        postId: postId,
        userId: 0, // Backend handles user identification
        userType: _userType,
      ),
    );
    _scheduleFlush();
  }

  // ─────────────────────────────────────────────
  //  ➜ 3.  Internal: debounce + flush
  // ─────────────────────────────────────────────
  void _scheduleFlush() {
    _flushTimer?.cancel();
    _flushTimer = Timer(const Duration(seconds: 5), _flushPendingViews);
  }

  Future<void> _flushPendingViews() async {
    if (_pendingViews.isEmpty) return;

    // copy ‑ then clear queue immediately (optimistic)
    final batch = List<PostViewPayload>.from(_pendingViews);
    _pendingViews.clear();

    final ok = await _repository.addMultiplePostViews(batch);
    if (!ok) {
      // put them back so we can retry on the next flush
      _pendingViews.insertAll(0, batch);
      // optionally: exponential-back-off, but simple retry next 10 s
      _flushTimer ??= Timer(const Duration(seconds: 10), _flushPendingViews);
    }
  }

  // ---------------- FETCH-ON-ENTRY DATA -----------------
  List<TagProfile> _tagProfiles = [];
  List<NurseryClassSection> _nurseryTree = [];
  List<AutocompleteStudent> _students = [];
  
  bool _loadingTagProfiles = false;
  bool _loadingNurseryTree = false;
  bool _loadingStudents = false;
  
  bool _tagProfilesLoaded = false;
  bool _nurseryTreeLoaded = false;
  bool _studentsLoaded = false;

  List<TagProfile> get tagProfiles => _tagProfiles;
  List<NurseryClassSection> get nurseryTree => _nurseryTree;
  List<AutocompleteStudent> get studentsList => _students;
  
  bool get loadingTagProfiles => _loadingTagProfiles;
  bool get loadingNurseryTree => _loadingNurseryTree;
  bool get loadingStudents => _loadingStudents;
  
  bool get loadingMeta => _loadingTagProfiles || _loadingNurseryTree || _loadingStudents;

  Future<void> loadCreatePostMeta() async {
    await Future.wait([
      loadTagProfiles(),
      loadNurseryClassSections(),
      loadStudentsForParent(),
    ]);
  }

  Future<void> loadTagProfiles() async {
    if (_loadingTagProfiles || _tagProfilesLoaded) return;
    
    _loadingTagProfiles = true;
    notifyListeners();
    
    try {
      debugPrint('🔄 Loading tag profiles...');
      _tagProfiles = await _repository.fetchTagProfiles().catchError((e) {
        debugPrint('fetchTagProfiles failed: $e');
        return <TagProfile>[];
      });
      _tagProfilesLoaded = true;
      debugPrint('✅ Tag profiles loaded: ${_tagProfiles.length} items');
    } catch (e) {
      debugPrint('loadTagProfiles failed: $e');
      _tagProfiles = [];
    } finally {
      _loadingTagProfiles = false;
      notifyListeners();
    }
  }

  Future<void> loadNurseryClassSections() async {
    if (_loadingNurseryTree || _nurseryTreeLoaded) return;
    
    _loadingNurseryTree = true;
    notifyListeners();
    
    try {
      debugPrint('🔄 Loading nursery class sections...');
      _nurseryTree = await _repository.fetchNurseryClassSections().catchError((e) {
        debugPrint('fetchNurseryClassSections failed: $e');
        return <NurseryClassSection>[];
      });
      _nurseryTreeLoaded = true;
      debugPrint('✅ Nursery class sections loaded: ${_nurseryTree.length} items');
    } catch (e) {
      debugPrint('loadNurseryClassSections failed: $e');
      _nurseryTree = [];
    } finally {
      _loadingNurseryTree = false;
      notifyListeners();
    }
  }

  Future<void> loadStudentsForParent() async {
    if (_loadingStudents || _studentsLoaded) return;
    
    _loadingStudents = true;
    notifyListeners();
    
    try {
      debugPrint('🔄 Loading students for parent...');
      _students = await _repository.fetchStudentsForParent().catchError((e) {
        debugPrint('fetchStudentsForParent failed: $e');
        return <AutocompleteStudent>[];
      });
      _studentsLoaded = true;
      debugPrint('✅ Students for parent loaded: ${_students.length} items');
    } catch (e) {
      debugPrint('loadStudentsForParent failed: $e');
      _students = [];
    } finally {
      _loadingStudents = false;
      notifyListeners();
    }
  }

  Future<void> refreshCreatePostMeta() async {
    _tagProfilesLoaded = false;
    _nurseryTreeLoaded = false;
    _studentsLoaded = false;
    
    _tagProfiles.clear();
    _nurseryTree.clear();
    _students.clear();
    
    await loadCreatePostMeta();
  }

  // ---------------- DRAFT / PUBLISH STATE ---------------
  Set<int> selectedTagUserIds = {};
  Set<int> selectedStudentIds = {};
  Set<int> selectedNurseryIds = {};      // for audience logic
  bool allowComments = true;
  File? pickedImage;                     // set by UI
  String postBody = '';

  void toggleTag(int userId) {
    if (selectedTagUserIds.contains(userId)) {
      selectedTagUserIds.remove(userId);
    } else {
      selectedTagUserIds.add(userId);
    }
    notifyListeners();
  }

  void toggleStudent(int studentId) {
    if (selectedStudentIds.contains(studentId)) {
      selectedStudentIds.remove(studentId);
    } else {
      selectedStudentIds.add(studentId);
    }
    notifyListeners();
  }

  void setSelectedNurseries(List<int> nurseryIds) {
    selectedNurseryIds = nurseryIds.toSet();
    notifyListeners();
  }

  void toggleNursery(int nurseryId) {
    if (selectedNurseryIds.contains(nurseryId)) {
      selectedNurseryIds.remove(nurseryId);
    } else {
      selectedNurseryIds.add(nurseryId);
    }
    notifyListeners();
  }

  void setAllowComments(bool v) {
    allowComments = v;
    notifyListeners();
  }

  void setPostBody(String body) {
    postBody = body;
    notifyListeners();
  }

  void setImage(File? f) {
    pickedImage = f;
    notifyListeners();
  }

  /// High-level publish
  Future<bool> publishPost({
    required int parentId,
    required int userType,            // 1 = parent
    required int academicYearId,
  }) async {
    // 1. OPTIONAL image upload
    String? fileKey, thumbKey;
    if (pickedImage != null) {
      try {
        final platformFile = PlatformFile(
          name: pickedImage!.path.split('/').last,
          path: pickedImage!.path,
          size: await pickedImage!.length(),
        );
        final uploadResult = await _parentRepository.uploadFileToS3(platformFile);
        fileKey = uploadResult['fileKeyName'] as String?;
        thumbKey = uploadResult['thumbnailKey'] as String?;
        if (fileKey == null) return false;
      } catch (e) {
        debugPrint('uploadFileToS3 failed: $e');
        return false;
      }
    }

    // 2. Build payload
    final body = <String, dynamic>{
      'userId': parentId,
      'userType': userType,
      'postBody': postBody,
      'location': '',
      'scope': 1,
      'status': 1,
      'academicYearId': academicYearId,
      'postAttachments': fileKey == null
          ? []
          : [
              {
                'attachmentKey': fileKey,
                'thumbnailKey': thumbKey,
                'attachmentType': 1, // 1 = image
              }
            ],
      'postStudentDetails': selectedStudentIds
          .map((e) => {'Id': null, 'studentId': e}).toList(),
      'postTags': selectedTagUserIds
          .map((e) => {
                'Id': null,
                'userType': 3, // because our tags are currently students
                'userId': e
              })
          .toList(),
      'postNurseries': selectedNurseryIds
          .map((nurseryId) => {'nurseryGradeId': nurseryId}).toList(),
      'postClasses': [],
      'postClassSections': [],
      'postType': 1,
      'publishedDate': null,
      'isAllowedToComment': allowComments,
      'isPostFeatured': false,
    };

    // 3. POST /CreatePost
    final ok = await _repository.createPost(body);
    if (ok) _resetDraft();
    return ok;
  }

  void _resetDraft() {
    selectedTagUserIds.clear();
    selectedStudentIds.clear();
    selectedNurseryIds.clear();
    pickedImage = null;
    postBody = '';
    allowComments = true;
    notifyListeners();
  }
  Future<bool> downloadAttachment(String? fileKey) async {
    if (fileKey == null || fileKey.isEmpty) return false;
    try {
      final bytes = await _repository.fetchPresignedUrlDownload(fileKey);
      if (bytes == null) return false;
      final fileName = fileKey.split('/').lastOrNull ?? 'attachment';
      await DownloadUtilsForBytes.saveAsBytes(bytes, fileName: fileName);
      return true;
    } catch (e) {
      debugPrint('downloadAttachment failed: $e');
      return false;
    }
  }
  // ─────────────────────────────────────────────
  //  ➜ 4.  Clean-up
  // ─────────────────────────────────────────────
  @override
  void dispose() {
    _flushTimer?.cancel();
    super.dispose();
  }
}