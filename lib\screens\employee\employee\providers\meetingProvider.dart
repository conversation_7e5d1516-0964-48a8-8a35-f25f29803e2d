import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/screens/employee/employee/repository/eventRepository.dart';
import 'package:seawork/screens/employee/employee/models/meeting.dart';

// State class for calendar
class CalendarState {
  final List<Meeting> meetings;
  final DateTime selectedDate;
  final bool showMonthView;
  final bool isLoading;
  final bool isRsvpLoading; // New loading state for RSVP actions
  final String? error;

  CalendarState({
    required this.meetings,
    required this.selectedDate,
    required this.showMonthView,
    required this.isLoading,
    this.isRsvpLoading = false,
    this.error,
  });

  // Create a copy of this state with possible changes
  CalendarState copyWith({
    List<Meeting>? meetings,
    DateTime? selectedDate,
    bool? showMonthView,
    bool? isLoading,
    bool? isRsvpLoading,
    String? error,
  }) {
    return CalendarState(
      meetings: meetings ?? this.meetings,
      selectedDate: selectedDate ?? this.selectedDate,
      showMonthView: showMonthView ?? this.showMonthView,
      isLoading: isLoading ?? this.isLoading,
      isRsvpLoading: isRsvpLoading ?? this.isRsvpLoading,
      error: error,
    );
  }
}

// Event provider notifier
class EventNotifier extends StateNotifier<CalendarState> {
  final EventRepository _eventRepository;

  EventNotifier(this._eventRepository)
    : super(
        CalendarState(
          meetings: [],
          selectedDate: DateTime.now(),
          showMonthView: false,
          isLoading: false,
        ),
      ) {
    // Fetch events when initialized
    fetchEvents();
  }

  // Fetch all events and convert to Meeting objects
  Future<void> fetchEvents() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Get events data from repository
      final events = await _eventRepository.fetchEvents();

      // Map the API data to Meeting objects
      final meetings = events.map((event) => Meeting.fromJson(event)).toList();

      state = state.copyWith(meetings: meetings, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  // Fetch events for a specific date range
  Future<void> fetchEventsForDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Get events data from repository
      final events = await _eventRepository.fetchEventsForDateRange(
        startDate,
        endDate,
      );

      // Map the API data to Meeting objects directly using the factory method
      final meetings = events.map((event) => Meeting.fromJson(event)).toList();

      state = state.copyWith(meetings: meetings, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  // Select a specific date
  void selectDate(DateTime date) {
    state = state.copyWith(selectedDate: date);

    // Optionally fetch events for this date and surrounding dates
    final startDate = date.subtract(const Duration(days: 7));
    final endDate = date.add(const Duration(days: 28)); // Fetch 4 weeks ahead
    fetchEventsForDateRange(startDate, endDate);
  }

  // Toggle between month view and week view
  void toggleCalendarView() {
    state = state.copyWith(showMonthView: !state.showMonthView);
  }

  // Refresh events (useful for pull-to-refresh)
  Future<void> refreshEvents() async {
    return fetchEvents();
  }

  void resetToToday() {
    final today = DateTime.now();
    state = state.copyWith(selectedDate: today);
    
    final startDate = today.subtract(const Duration(days: 7));
    final endDate = today.add(const Duration(days: 28));
    fetchEventsForDateRange(startDate, endDate);
  }

  // Accept a meeting
  Future<bool> acceptMeeting(
    String eventId, {
    String? comment,
    bool sendResponse = true,
  }) async {
    try {
      state = state.copyWith(isRsvpLoading: true, error: null);

      final success = await _eventRepository.acceptMeeting(
        eventId,
        comment: comment,
        sendResponse: sendResponse,
      );

      if (success) {
        // Refresh events to get updated status
        await refreshEvents();
      }

      state = state.copyWith(isRsvpLoading: false);
      return success;
    } catch (e) {
      state = state.copyWith(isRsvpLoading: false, error: e.toString());
      return false;
    }
  }

  // Decline a meeting
  Future<bool> declineMeeting(
    String eventId, {
    String? comment,
    bool sendResponse = true,
  }) async {
    try {
      state = state.copyWith(isRsvpLoading: true, error: null);

      final success = await _eventRepository.declineMeeting(
        eventId,
        comment: comment,
        sendResponse: sendResponse,
      );

      if (success) {
        // Refresh events to get updated status
        await refreshEvents();
      }

      state = state.copyWith(isRsvpLoading: false);
      return success;
    } catch (e) {
      state = state.copyWith(isRsvpLoading: false, error: e.toString());
      return false;
    }
  }
}

// Provider for calendar state
final graphCalendarProvider =
    StateNotifierProvider<EventNotifier, CalendarState>((ref) {
      final eventRepository = EventRepository();
      return EventNotifier(eventRepository);
    });