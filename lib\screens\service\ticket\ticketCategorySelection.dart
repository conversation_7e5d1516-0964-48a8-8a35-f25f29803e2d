import 'dart:convert';

import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/image/renderImage.dart';
import 'package:seawork/components/box/container.dart';
import 'package:seawork/components/list/renderItem.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/list/renderList.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/customSubmitConfirmDialog.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/constants/constantTexts.dart';
import 'package:seawork/screens/service/ticket/models/serviceCategory.dart';
import 'package:seawork/screens/service/ticket/providers/ticketProviders.dart';
import 'package:seawork/screens/service/ticket/repository/requestAndReport.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/utils/util.dart';

final isSubmitButtonActiveProvider = StateProvider<bool>((ref) => false);
final descriptionTextProvider = StateProvider<String>((ref) => '');
final isSubmittingProvider = StateProvider<bool>((ref) => false);
final uploadedFilesProvider = StateProvider<List<FileModel>>((ref) => []);

class TicketCategorySelection extends ConsumerWidget {
  final String iconClicked;
  final String? requestType;
  final String? prefilledDescription;
  final int itilTicketTypeId;

  const TicketCategorySelection({
    Key? key,
    required this.iconClicked,
    required this.itilTicketTypeId,
    this.requestType,
    this.prefilledDescription,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final serviceCategoriesAsync = ref.watch(
      serviceCategoriesProvider(itilTicketTypeId),
    );
    return SecondaryScaffoldWithAppBar(
      context,
      iconClicked,
      SvgImage24x24('assets/images/appbackbutton.svg'),
      () {
        // if (requestType != null) {
        //   Navigator.pop(context);
        // } else {
        //   NavigateToMainPage(context, Help.tag);
        // }
        Navigator.pop(context);
      },
      bodyItems: [
        const SizedBox(height: 12),
        RenderListWithLoader(serviceCategoriesAsync, (category) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: RenderListItemWithTextAndIcon(
              context,
              category,
              category.icon != null
                  ? NetworkImage44x45(category.icon ?? "")
                  : SvgImage44x45(_getServiceIcon(category.id)),
              capitalizeFirstWordOnly(category.name ?? ''),
              (category) => {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => TicketRequestPage(
                          category: category,
                          itilTicketTypeId: itilTicketTypeId,
                          iconClicked: iconClicked,
                          requestType: requestType,
                          prefilledDescription: prefilledDescription,
                        ),
                  ),
                ),
              },
            ),
          );
        }),
      ],
    );
  }

  String _getServiceIcon(int? serviceId) {
    return 'assets/images/ITservicereq.svg';
  }
}

class TicketRequestPage extends ConsumerStatefulWidget {
  final ServiceCategory category;
  final int? itilTicketTypeId;
  final String? iconClicked;
  final String? requestType;
  final String? prefilledDescription;

  const TicketRequestPage({
    Key? key,
    required this.category,
    this.itilTicketTypeId,
    this.iconClicked,
    this.requestType,
    this.prefilledDescription,
  }) : super(key: key);

  @override
  ConsumerState<TicketRequestPage> createState() => _TicketRequestPageState();
}

class _TicketRequestPageState extends ConsumerState<TicketRequestPage> {
  late TextEditingController descriptionController;

  @override
  void initState() {
    super.initState();
    descriptionController = TextEditingController();

    if (widget.prefilledDescription != null) {
      descriptionController.text = widget.prefilledDescription!;
      ref.read(descriptionTextProvider.notifier).state =
          widget.prefilledDescription!;
      ref.read(isSubmitButtonActiveProvider.notifier).state = true;
    }
  }

  @override
  void dispose() {
    descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bool fromSearch = widget.requestType != null;
    final serviceItemDetails = ref.watch(
      getServiceWithGeneralRequestFilterProvider(widget.category.id ?? 0),
    );
    final String appBarTitle = _getAppBarTitle(widget.category.name ?? '');
    final isSubmitting = ref.watch(isSubmittingProvider);

    return Stack(
      children: [
        SecondaryScaffoldWithAppBar(
          context,
          appBarTitle,
          SvgImage24x24('assets/images/appbackbutton.svg'),
          () {
            if (!isSubmitting) Navigator.pop(context);
          },
          bodyItems: [
            serviceItemDetails.when(
              loading:
                  () => const Expanded(
                    child: Center(child: CircularProgressIndicator()),
                  ),
              error:
                  (error, stack) =>
                      Expanded(child: Center(child: Text('Error: $error'))),
              data: (details) {
                final detailRequestDetails =
                    details.serviceRequestDetails?.isNotEmpty == true
                        ? details.serviceRequestDetails?.first
                        : null;

                if (detailRequestDetails == null) {
                  return Expanded(
                    child: Center(
                      child: OpenSansText(
                        'No ticket type details available',
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  );
                }

                final requestTypeId =
                    detailRequestDetails.servicedetailRequesttypeid;

                final ticketTypeDetails = ref.watch(
                  ticketTypeByIdProvider(requestTypeId!),
                );

                return ticketTypeDetails.when(
                  loading:
                      () => const Expanded(
                        child: Center(child: CircularProgressIndicator()),
                      ),
                  error:
                      (error, stack) =>
                          Expanded(child: Center(child: Text('Error: $error'))),
                  data: (ticketType) {
                    if (ticketType.id == 0) {
                      return Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(24),
                              child: Center(
                                child: OpenSansText(
                                  'This ticket type is not found for creation',
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                            const Spacer(),
                          ],
                        ),
                      );
                    }

                    var fields = ticketType.fields;

                    if (fields == null || fields.isEmpty) {
                      return Expanded(
                        child: Center(
                          child: OpenSansText(
                            'No fields available for this ticket type',
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      );
                    }

                    var mandatoryFields = ["Summary", "Details", "Agent"];

                    var mandatoryFieldLabels =
                        fields.where((field) {
                          return mandatoryFields.contains(
                            field.fieldinfo?.label,
                          );
                        }).toList();

                    if (mandatoryFieldLabels.isEmpty) {
                      return Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(24),
                              child: Center(
                                child: OpenSansText(
                                  'This ticket type requires more fields than we expect, please do via web',
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                            const Spacer(),
                          ],
                        ),
                      );
                    }

                    return Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 12),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                BuildTextField(
                                  label: 'Describe the issue',
                                  hintText: 'Enter',
                                  maxLines: 4,
                                  controller: descriptionController,
                                  onChanged: (value) {
                                    ref
                                        .read(descriptionTextProvider.notifier)
                                        .state = value;
                                    ref
                                        .read(
                                          isSubmitButtonActiveProvider.notifier,
                                        )
                                        .state = value.isNotEmpty;
                                  },
                                ),
                                const SizedBox(height: 30),
                                HeadingText(text: 'Attachments'),
                                SizedBox(height: 8),
                                AttachmentField(
                                  uploadedFiles: ref.watch(
                                    uploadedFilesProvider,
                                  ),
                                  onFilesChanged: (files) {
                                    ref
                                        .read(uploadedFilesProvider.notifier)
                                        .state = files;
                                  },
                                ),
                              ],
                            ),
                          ),
                          const Spacer(),
                          SafeArea(
                            child: Padding(
                              padding: const EdgeInsets.all(24),
                              child: Consumer(
                                builder: (context, ref, child) {
                                  final isSubmitButtonActive = ref.watch(
                                    isSubmitButtonActiveProvider,
                                  );

                                  return ElevatedButton(
                                    onPressed:
                                        isSubmitting || !isSubmitButtonActive
                                            ? null
                                            : () async {
                                              ref
                                                  .read(
                                                    isSubmittingProvider
                                                        .notifier,
                                                  )
                                                  .state = true;
                                              try {
                                                final description = ref.read(
                                                  descriptionTextProvider,
                                                );
                                                final attachments =
                                                    ref
                                                        .read(
                                                          uploadedFilesProvider,
                                                        )
                                                        .map((file) {
                                                          return {
                                                            "filename":
                                                                file.name,
                                                            "data_base64":
                                                                "data:${file.mimeType};base64,${base64Encode(file.bytes)}",
                                                            "tickettype_id":
                                                                requestTypeId,
                                                            "allow_anon_upload":
                                                                false,
                                                            "_uploading": false,
                                                            "showforusers":
                                                                true,
                                                            "datecreated":
                                                                DateTime.now()
                                                                    .toIso8601String(),
                                                          };
                                                        })
                                                        .toList();

                                                try {
                                                  final result = await ref.read(
                                                    createTicketProvider((
                                                      details: description,
                                                      summary:
                                                          widget
                                                              .category
                                                              .name ??
                                                          "",
                                                      attachments:
                                                          attachments.isNotEmpty
                                                              ? attachments
                                                              : null,
                                                      ticketTypeId:
                                                          requestTypeId,
                                                    )).future,
                                                  );

                                                  if (result) {
                                                    ref
                                                        .read(
                                                          uploadedFilesProvider
                                                              .notifier,
                                                        )
                                                        .state = [];
                                                    ref
                                                        .read(
                                                          descriptionTextProvider
                                                              .notifier,
                                                        )
                                                        .state = '';
                                                    ref.refresh(
                                                      ticketsProvider(
                                                        'All tickets',
                                                      ),
                                                    );
                                                    ref
                                                        .read(
                                                          ticketPaginationProvider
                                                              .notifier,
                                                        )
                                                        .refreshTickets();
                                                    await showDialog(
                                                      context: context,
                                                      barrierDismissible: false,
                                                      builder:
                                                          (
                                                            context,
                                                          ) => CustomSubmitConfirmDialog(
                                                            content1: 'Ticket',
                                                            type: 'type',
                                                            content3:
                                                                _getRequestTypeName(
                                                                  widget
                                                                      .category
                                                                      .name,
                                                                ),
                                                            message:
                                                                ConstantTexts
                                                                    .requestPopupmessage,
                                                            documentType: '',
                                                            document: null,
                                                            onClose: () {
                                                              Navigator.of(
                                                                context,
                                                              ).pop();
                                                              Navigator.of(
                                                                context,
                                                              ).pop();
                                                              if (fromSearch) {
                                                                Navigator.of(
                                                                  context,
                                                                ).pop();
                                                              }
                                                            },
                                                          ),
                                                    );
                                                  }
                                                } catch (e) {
                                                  ScaffoldMessenger.of(
                                                    context,
                                                  ).showSnackBar(
                                                    SnackBar(
                                                      content: OpenSansText(
                                                        'Failed to create ticket ${e.toString()}',
                                                        fontSize: 12,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                      ),
                                                      backgroundColor:
                                                          AppColors.red,
                                                    ),
                                                  );
                                                }
                                              } finally {
                                                ref
                                                    .read(
                                                      isSubmittingProvider
                                                          .notifier,
                                                    )
                                                    .state = false;
                                              }
                                            },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppColors.viewColor,
                                      minimumSize: const Size(
                                        double.infinity,
                                        56,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: DmSansText(
                                      'Submit request',
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color:
                                          isSubmitButtonActive
                                              ? AppColors.whiteColor
                                              : AppColors.lightGreyColor,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          ],
        ),
        if (isSubmitting)
          ModalBarrier(
            color: AppColors.blackColor.withOpacity(0.3),
            dismissible: false,
          ),
        if (isSubmitting)
          const Center(
            child: CircularProgressIndicator(color: AppColors.viewColor),
          ),
      ],
    );
  }

  String _getAppBarTitle(String iconClicked) {
    switch (widget.category.name) {
      case 'IT':
        return 'IT service request';
      case 'Facilities':
        return 'Facilities request';
      case 'Academic Requests':
        return 'Academic request';
      case 'Translation Request':
        return 'Translation request';
      default:
        return widget.category.name;
    }
  }

  String _getRequestTypeName(String? categoryName) {
    if (categoryName == null) return 'Request';
    if (categoryName == 'Facilities') return 'Facilities request';
    if (categoryName == 'IT') return 'IT service request';
    if (categoryName == 'Academic Requests') return 'Academic request';
    if (categoryName == 'Translation Request') return 'Translation request';
    return categoryName;
  }
}
