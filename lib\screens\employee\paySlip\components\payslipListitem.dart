import 'package:flutter/material.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/paySlip/models/payslipModel.dart';
import 'package:seawork/utils/style/colors.dart';

class PaySlipListItem extends StatelessWidget {
  final PaySlip paySlip;
  final String formattedDate;
  final VoidCallback onTap;
  final VoidCallback onDownload;
  final bool isDownloading;
  final bool showDateInTitle;
  final VoidCallback? onDismissed; // Add callback for when item is swiped away

  const PaySlipListItem({
    Key? key,
    required this.paySlip,
    required this.formattedDate,
    required this.onTap,
    required this.onDownload,
    required this.isDownloading,
    this.showDateInTitle = true,
    this.onDismissed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: size.width * 0.05),
      child: GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.only(bottom: size.height * 0.01),
        child: Container(
          width: double.infinity,
          height: size.height * 0.09,
          decoration: BoxDecoration(
            color: AppColors.whiteColor,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: AppColors.boxshadow.withOpacity(0.4),
                blurRadius: 5.6,
                offset: Offset.zero,
              ),
            ],
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(width: 8),
              CustomSvgImage(imageName: 'payslipdoc', width: 44, height: 44),
              SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    FittedBox(
                      fit: BoxFit.scaleDown,
                      alignment: Alignment.centerLeft,
                      child: DmSansText(
                        showDateInTitle
                            ? ' Pay slip -$formattedDate'
                            : ' Pay slip',
                        fontSize: size.width * 0.04,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textColor,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              Flexible(
                flex: 0,
                child: ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: size.width * 0.1),
                  child:
                      isDownloading
                          ? SizedBox(
                            width: size.width * 0.06,
                            height: size.width * 0.06,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.primaryColor,
                              ),
                            ),
                          )
                          : IconButton(
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                            iconSize: size.width * 0.06,
                            icon: CustomSvgImage(
                              imageName: 'Icon_download',
                              width: size.width * 0.06,
                              height: size.width * 0.06,
                            ),
                            onPressed: onDownload,
                          ),
                ),
              ),
              SizedBox(width: size.width * 0.04),
            ],
          ),
          ),
        ),
      ),
    );
  }
}