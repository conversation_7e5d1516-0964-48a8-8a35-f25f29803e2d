import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/calender/components/calendarTimeUtils.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/calender/components/meetingDetails.dart';
import 'package:seawork/screens/employee/employee/providers/meetingProvider.dart';
import 'package:seawork/screens/employee/employee/models/meeting.dart';
import 'package:seawork/utils/util.dart';

class AgendaScreen extends ConsumerWidget {
  final DateTime selectedDate;

  const AgendaScreen({Key? key, required this.selectedDate}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final calendarNotifier = ref.read(graphCalendarProvider.notifier);
    final calendarState = ref.watch(graphCalendarProvider);
    final List<Meeting> allMeetings = calendarState.meetings;
    final Map<String, List<Meeting>> meetingsByDate = {};

    // Group meetings by date
    for (final meeting in allMeetings) {
      final meetingStart = TimeUtils.parseMeetingTime(
        meeting.time,
        DateTime.parse(meeting.date),
      );
      final meetingEnd = meetingStart.add(
        Duration(minutes: meeting.durationMinutes),
      );

      // Add meeting to all dates it spans
      if (meetingStart.day == meetingEnd.day) {
        // Single day meeting
        if (!meetingsByDate.containsKey(meeting.date)) {
          meetingsByDate[meeting.date] = [];
        }
        meetingsByDate[meeting.date]!.add(meeting);
      } else {
        // Multi-day meeting - add to each day it spans
        DateTime currentDate = DateTime(
          meetingStart.year,
          meetingStart.month,
          meetingStart.day,
        );
        final endDate = DateTime(
          meetingEnd.year,
          meetingEnd.month,
          meetingEnd.day,
        );

        while (currentDate.isBefore(endDate) ||
            currentDate.isAtSameMomentAs(endDate)) {
          final dateKey = TimeUtils.formatDateForComparison(currentDate);
          if (!meetingsByDate.containsKey(dateKey)) {
            meetingsByDate[dateKey] = [];
          }
          meetingsByDate[dateKey]!.add(meeting);
          currentDate = currentDate.add(const Duration(days: 1));
        }
      }
    }

    final List<DateTime> datesRange = List.generate(
      28,
      (index) => selectedDate.add(Duration(days: index)),
    );

    return Container(
      color: AppColors.secondaryColor,
      // child: RefreshIndicator(
      //   onRefresh:
      //       () => ref.read(graphCalendarProvider.notifier).refreshEvents(),
      child: ListView.builder(
        physics: const BouncingScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        itemCount: datesRange.length,
        itemBuilder: (context, index) {
          final date = datesRange[index];
          return _buildDateSection(date, meetingsByDate, context, calendarNotifier);
        },
      ),
      // ),
    );
  }

  Widget _buildDateSection(
    DateTime date,
    Map<String, List<Meeting>> meetingsByDate,
    BuildContext context,
    EventNotifier calendarNotifier,
  ) {
    final dateString = TimeUtils.formatDateForComparison(date);
    final meetings = meetingsByDate[dateString] ?? [];

    // Sort meetings by their actual start time (considering multi-day meetings)
    meetings.sort((a, b) {
      final aStart = TimeUtils.parseMeetingTime(a.time, DateTime.parse(a.date));
      final bStart = TimeUtils.parseMeetingTime(b.time, DateTime.parse(b.date));
      return aStart.compareTo(bStart);
    });

    final formattedDate = DateFormat('MMM d').format(date);
    final dayLabel =
        TimeUtils.isToday(date)
            ? "Today"
            : TimeUtils.isTomorrow(date)
            ? "Tomorrow"
            : DateFormat('EEEE').format(date);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Date header
        Padding(
          padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
          child: Row(
            children: [
              DmSansText(
                formattedDate,
                fontWeight: FontWeight.w500,
                fontSize: 16,
                color: AppColors.viewColor,
              ),
              const SizedBox(width: 8),
              OpenSansText(
                dayLabel,
                fontSize: 12,
                color: AppColors.darkGreyColor,
                fontWeight: FontWeight.w400,
              ),
            ],
          ),
        ),

        if (meetings.isEmpty)
          Padding(
            padding: EdgeInsets.only(bottom: 16.0),
            child: OpenSansText(
              'No events',
              fontWeight: FontWeight.w400,
              fontSize: 12,
              color: AppColors.darkGreyColor,
            ),
          )
        else
          _buildMeetingsTimeline(meetings, date, context, calendarNotifier),
      ],
    );
  }

  Widget _buildMeetingsTimeline(
    List<Meeting> meetings,
    DateTime meetingDate,
    BuildContext context,
    EventNotifier calendarNotifier,
  ) {
    return Stack(
      children: [
        // Continuous timeline line - positioned absolutely
        Positioned(
          left: 68.0,
          top: 0,
          bottom: 0,
          child: Container(width: 2, color: AppColors.meetingdurationcolor),
        ),

        Column(
          children:
              meetings.map((meeting) {
                final meetingStart = TimeUtils.parseMeetingTime(
                  meeting.time,
                  DateTime.parse(meeting.date),
                );
                final meetingEnd = meetingStart.add(
                  Duration(minutes: meeting.durationMinutes),
                );
                final isMultiDay = meetingStart.day != meetingEnd.day;
                final isFirstDay = meetingStart.day == meetingDate.day;
                final isLastDay = meetingEnd.day == meetingDate.day;

                return Padding(
                  padding: EdgeInsets.only(bottom: 16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Time and duration column - now left-aligned
                      SizedBox(
                        width: 60.0,
                        child: Padding(
                          padding: const EdgeInsets.only(top: 16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              DmSansText(
                                TimeUtils.formatTimeToDisplay(
                                  meeting.time,
                                  meetingDate,
                                ),
                                fontWeight: FontWeight.w400,
                                fontSize: 12,
                                color: AppColors.meetingdurationcolor,
                              ),
                              const SizedBox(height: 8),
                              DmSansText(
                                '${meeting.durationMinutes} min',
                                color: AppColors.darkGreyColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 18),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(
                            bottom: 10.0,
                            left: 10,
                            top: 2,
                          ),
                          child: _buildMeetingCard(
                            meeting,
                            meetingDate,
                            context,
                            calendarNotifier,
                            isMultiDay: isMultiDay,
                            isFirstDay: isFirstDay,
                            isLastDay: isLastDay,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildMeetingCard(
    Meeting meeting,
    DateTime meetingDate,
    BuildContext context,
    EventNotifier calendarNotifier, {
    bool isMultiDay = false,
    bool isFirstDay = false,
    bool isLastDay = false,
  }) {
    String locationText = "Microsoft Teams";
    if (meeting.location != null && meeting.location!.isNotEmpty) {
      locationText = meeting.location!;
    } else if (meeting.isOnline && meeting.onlineMeetingProvider.isNotEmpty) {
      if (meeting.onlineMeetingProvider == 'teamsForBusiness') {
        locationText = "Microsoft Teams";
      } else {
        locationText = meeting.onlineMeetingProvider;
      }
    }
    final isEnded = TimeUtils.isMeetingEnded(meeting);
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        splashColor: AppColors.microinteraction,
        highlightColor: AppColors.microinteraction,
        onTap: () async {
          await Future.delayed(const Duration(milliseconds: 100));
          await Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => MeetingDetailsScreen(meeting: meeting),
            ),
          );
          if (context.mounted) {
            calendarNotifier.resetToToday();
          }
        },
        child: Ink(
          decoration: BoxDecoration(
            color: AppColors.calendermeetColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.calendermeetborderColor,
              width: 0.5,
            ),
            boxShadow: const [
              BoxShadow(color: AppColors.boxshadow, offset: Offset(0, 0)),
            ],
          ),
          padding: const EdgeInsets.only(
            right: 18,
            left: 18,
            top: 20,
            bottom: 24,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DmSansText(
                meeting.title,
                color:
                    (meeting.isCancelled || isEnded)
                        ? AppColors.darkGreyColor
                        : AppColors.blackColor,
                fontWeight: FontWeight.w600,
                fontSize: 16,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textDecoration:
                    meeting.isCancelled
                        ? TextDecoration.lineThrough
                        : TextDecoration.none,
              ),
              SizedBox(height: 12),
              DmSansText(
                capitalizeFirstWordOnly(locationText),
                color:
                    meeting.isCancelled
                        ? AppColors.darkGreyColor
                        : AppColors.lightBlack,
                fontSize: 12,
                fontWeight: FontWeight.w500,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}